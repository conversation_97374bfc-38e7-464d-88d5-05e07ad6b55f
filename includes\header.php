<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'POS Application'; ?></title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Mobile Navigation Toggle -->
            <nav class="navbar navbar-dark d-md-none">
                <div class="container-fluid">
                    <span class="navbar-brand mb-0 h1">
                        <i class="fas fa-store"></i> POS System
                    </span>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>
            </nav>

            <!-- Sidebar -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-store"></i> POS System
                        </h4>
                        <small class="text-white-50">Point of Sale</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'dashboard' ? 'active' : ''; ?>" href="<?php echo getNavUrl('dashboard'); ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>

                        <!-- Master Data Section -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>MASTER DATA</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'categories' ? 'active' : ''; ?>" href="<?php echo getNavUrl('categories'); ?>">
                                <i class="fas fa-tags me-2"></i>
                                Kategori
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'products' ? 'active' : ''; ?>" href="<?php echo getNavUrl('products'); ?>">
                                <i class="fas fa-box me-2"></i>
                                Data Produk
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'customers' ? 'active' : ''; ?>" href="<?php echo getNavUrl('customers'); ?>">
                                <i class="fas fa-users me-2"></i>
                                Pelanggan
                            </a>
                        </li>

                        <!-- Transaction Section -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>TRANSAKSI</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'pos' ? 'active' : ''; ?>" href="<?php echo getNavUrl('pos'); ?>">
                                <i class="fas fa-cash-register me-2"></i>
                                Form Penjualan
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'sales' ? 'active' : ''; ?>" href="<?php echo getNavUrl('sales'); ?>">
                                <i class="fas fa-receipt me-2"></i>
                                Riwayat Penjualan
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'outstanding_dp' ? 'active' : ''; ?>" href="<?php echo getNavUrl('outstanding_dp'); ?>">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Outstanding DP
                            </a>
                        </li>

                        <!-- Reports Section -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>LAPORAN</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage ?? '') == 'reports' ? 'active' : ''; ?>" href="<?php echo getNavUrl('reports'); ?>">
                                <i class="fas fa-chart-bar me-2"></i>
                                Laporan
                            </a>
                        </li>
                    </ul>

                    <hr class="text-white-50">

                    <!-- Quick Actions -->
                    <div class="px-3 mb-3">
                        <h6 class="sidebar-heading text-white-50 mb-2">AKSI CEPAT</h6>
                        <div class="d-grid gap-2">
                            <a href="<?php echo getNavUrl('pos'); ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Form Penjualan
                            </a>
                            <a href="<?php echo getNavUrl('products'); ?>" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-box me-1"></i>
                                Tambah Produk
                            </a>
                        </div>
                    </div>

                    <hr class="text-white-50">

                    <div class="text-center">
                        <small class="text-white-50">
                            © 2025 POS System<br>
                            Version 1.0
                        </small>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Breadcrumb -->
                <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
                <nav aria-label="breadcrumb" class="pt-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?php echo getNavUrl('dashboard'); ?>">
                                <i class="fas fa-home"></i> Dashboard
                            </a>
                        </li>
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url'])): ?>
                                <li class="breadcrumb-item">
                                    <a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a>
                                </li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <?php echo $item['title']; ?>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </nav>
                <?php endif; ?>

                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if (isset($pageIcon)): ?>
                            <i class="<?php echo $pageIcon; ?> me-2"></i>
                        <?php endif; ?>
                        <?php echo $pageTitle ?? 'Dashboard'; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('d F Y'); ?>
                            </span>
                        </div>
                        <?php if (isset($pageActions)): ?>
                            <div class="btn-group">
                                <?php echo $pageActions; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php
                // Display flash messages
                if (isset($_SESSION['flash_message'])) {
                    $flash = $_SESSION['flash_message'];
                    echo '<div class="alert alert-' . $flash['type'] . ' alert-dismissible fade show" role="alert">';
                    echo $flash['message'];
                    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
                    echo '</div>';
                    unset($_SESSION['flash_message']);
                }
                ?>

                <!-- Content Area -->
