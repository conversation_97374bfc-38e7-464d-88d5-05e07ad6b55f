<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Riwayat Penjualan';
$currentPage = 'sales';
$pageIcon = 'fas fa-receipt';
$breadcrumb = [
    ['title' => 'Transaksi', 'url' => '#'],
    ['title' => 'Riwayat Penjualan']
];

include '../includes/header.php';
?>

<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-white">
            <i class="fas fa-receipt me-2"></i>
            Riwayat Penjualan
        </h6>
    </div>
    <div class="card-body">
        <!-- Filter Controls -->
        <div class="row mb-3">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label"><PERSON><PERSON></label>
                <input type="date" class="form-control" id="dateFrom" placeholder="Pilih tanggal mulai">
                <div class="form-text">Kosongkan untuk tampilkan semua</div>
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">Sampai Tanggal</label>
                <input type="date" class="form-control" id="dateTo" placeholder="Pilih tanggal akhir">
                <div class="form-text">Kosongkan untuk tampilkan semua</div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-primary" id="filterSales">
                        <i class="fas fa-filter me-1"></i>
                        Filter
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-secondary" id="resetFilter">
                        <i class="fas fa-undo me-1"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Status Info -->
        <div class="row mb-2">
            <div class="col-12">
                <div id="filterStatus" class="alert alert-info py-2 mb-0" style="display: none;">
                    <i class="fas fa-info-circle me-1"></i>
                    <span id="filterStatusText"></span>
                </div>
            </div>
        </div>

        <!-- Sales Table -->
        <div class="table-responsive">
            <table id="salesTable" class="table table-striped table-hover" style="width:100%">
                <thead>
                    <tr>
                        <th>Invoice</th>
                        <th>Tanggal</th>
                        <th>Pelanggan</th>
                        <th>Total</th>
                        <th>Laba</th>
                        <th>Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Sale Detail Modal -->
<div class="modal fade" id="saleDetailModal" tabindex="-1" aria-labelledby="saleDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="saleDetailModalLabel">Detail Transaksi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="saleDetailContent">
                <!-- Content will be loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-success" id="printFromModal">
                    <i class="fas fa-print me-1"></i>
                    Cetak Struk
                </button>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script>
$(document).ready(function() {
    // Leave date filters empty by default to show all transactions
    $("#dateFrom").val("");
    $("#dateTo").val("");

    // Initialize DataTable
    var table = $("#salesTable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "../api/sales.php",
            type: "POST",
            data: function(d) {
                d.date_from = $("#dateFrom").val();
                d.date_to = $("#dateTo").val();
            }
        },
        columns: [
            { data: 0, width: "15%" },
            { data: 1, width: "10%" },
            { data: 2, width: "20%" },
            { data: 3, width: "15%" },
            { data: 4, width: "15%" },
            { data: 5, width: "15%" },
            { data: 6, width: "10%", orderable: false }
        ],
        order: [[5, "desc"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true
    });

    // Update filter status display
    function updateFilterStatus() {
        var dateFrom = $("#dateFrom").val();
        var dateTo = $("#dateTo").val();

        if (dateFrom || dateTo) {
            var statusText = "Menampilkan transaksi";
            if (dateFrom && dateTo) {
                statusText += " dari " + formatDateDisplay(dateFrom) + " sampai " + formatDateDisplay(dateTo);
            } else if (dateFrom) {
                statusText += " mulai dari " + formatDateDisplay(dateFrom);
            } else if (dateTo) {
                statusText += " sampai " + formatDateDisplay(dateTo);
            }

            $("#filterStatusText").text(statusText);
            $("#filterStatus").show();
        } else {
            $("#filterStatus").hide();
        }
    }

    // Format date for display
    function formatDateDisplay(dateString) {
        if (!dateString) return "";
        var date = new Date(dateString);
        return date.toLocaleDateString("id-ID", {
            day: "2-digit",
            month: "long",
            year: "numeric"
        });
    }

    // Filter sales
    $("#filterSales").on("click", function() {
        updateFilterStatus();
        table.ajax.reload();
    });

    // Reset filter
    $("#resetFilter").on("click", function() {
        $("#dateFrom").val("");
        $("#dateTo").val("");
        $("#filterStatus").hide();
        table.ajax.reload();
    });

    // Auto filter when date changes
    $("#dateFrom, #dateTo").on("change", function() {
        updateFilterStatus();
        table.ajax.reload();
    });

    // View sale detail
    $(document).on("click", ".btn-view", function() {
        var id = $(this).data("id");

        $.ajax({
            url: "../api/sale_detail.php",
            type: "GET",
            data: { id: id },
            success: function(response) {
                if (response.success) {
                    $("#saleDetailContent").html(response.data.html);
                    $("#printFromModal").data("sale-id", id);
                    $("#saleDetailModal").modal("show");
                } else {
                    showError(response.message);
                }
            }
        });
    });

    // Print receipt
    $(document).on("click", ".btn-print", function() {
        var id = $(this).data("id");
        printReceipt(id);
    });

    // Print from modal
    $("#printFromModal").on("click", function() {
        var id = $(this).data("sale-id");
        printReceipt(id);
    });

    // Payment button for DP transactions
    $(document).on("click", ".btn-payment", function() {
        var saleId = $(this).data("id");

        // Redirect to Outstanding DP page with specific transaction
        window.location.href = "outstanding_dp.php?highlight=" + saleId;
    });

    // Delete sale
    $(document).on("click", ".btn-delete", function() {
        var id = $(this).data("id");

        confirmDelete(function() {
            $.ajax({
                url: "../api/sale_delete.php",
                type: "POST",
                data: { id: id },
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload();
                        showSuccess(response.message);
                    } else {
                        showError(response.message);
                    }
                }
            });
        }, "Transaksi yang dihapus tidak dapat dikembalikan dan stok produk akan dikembalikan!");
    });

    function printReceipt(saleId) {
        window.open("../api/print_receipt.php?id=" + saleId, "_blank");
    }
});
</script>
';

include '../includes/footer.php';
?>
