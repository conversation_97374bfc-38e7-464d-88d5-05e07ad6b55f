            </main>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- SheetJS for Excel Export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo getBaseUrl(); ?>assets/js/app.js"></script>

    <script>
        // Additional global functions (functions already defined in app.js)

        // Parse number function for compatibility
        function parseNumber(formattedNumber) {
            if (!formattedNumber) return 0;
            return parseInt(String(formattedNumber).replace(/[^\d]/g, '')) || 0;
        }

        // Auto dismiss alerts
        $(document).ready(function() {
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });

        // Number input formatting - exclude discount inputs to avoid conflicts
        $(document).on('input', '.currency-input:not(.discount-input)', function() {
            let value = this.value.replace(/[^\d]/g, '');
            if (value) {
                this.value = formatNumber(parseInt(value));
            } else {
                this.value = '';
            }
        });

        // Prevent form double submission
        $('form').on('submit', function() {
            $(this).find('button[type="submit"]').prop('disabled', true);
        });
    </script>

    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
</body>
</html>
