<?php
// Test print receipt dengan informasi toko yang baru

// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once '../config/database.php';
require_once '../includes/functions.php';

echo "<h2>🧾 Test Print Receipt - UD. Nadia <PERSON></h2>";

try {
    global $db;

    // Get sample sale data
    $sampleSale = $db->fetchAll("SELECT * FROM sales ORDER BY id DESC LIMIT 1");

    if (empty($sampleSale)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h4>⚠️ Tidak Ada Data Transaksi</h4>";
        echo "<p>Belum ada transaksi untuk di-test print. Silakan buat transaksi dulu melalui Form Penjualan.</p>";
        echo "<p><a href='../pages/pos.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Buat Transaksi Baru</a></p>";
        echo "</div>";
        exit;
    }

    $sale = $sampleSale[0];

    echo "<h3>📋 Sample Data untuk Test Print:</h3>";
    echo "<table border='1' cellpadding='8' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Value</th></tr>";
    echo "<tr><td><strong>Invoice</strong></td><td>{$sale['invoice_number']}</td></tr>";
    echo "<tr><td><strong>Customer</strong></td><td>{$sale['customer_name']}</td></tr>";
    echo "<tr><td><strong>Tanggal</strong></td><td>" . formatDate($sale['sale_date']) . "</td></tr>";
    echo "<tr><td><strong>Total</strong></td><td>" . formatRupiah($sale['total_amount']) . "</td></tr>";
    echo "<tr><td><strong>Status</strong></td><td>" . ($sale['payment_status'] === 'partial' ? 'DP (Belum Lunas)' : 'Lunas') . "</td></tr>";
    echo "</table>";

    echo "<h3>🖨️ Preview Struk:</h3>";
    echo "<div style='background: #f8f9fa; border: 2px dashed #6c757d; padding: 20px; border-radius: 10px; max-width: 400px; margin: 20px 0;'>";
    echo "<div style='text-align: center; font-family: monospace; font-size: 14px; line-height: 1.4;'>";

    // Header
    echo "<div style='border-bottom: 1px dashed #000; padding-bottom: 10px; margin-bottom: 10px;'>";
    echo "<div style='font-size: 18px; font-weight: bold; margin-bottom: 5px;'>UD. NADIA MEBEL</div>";
    echo "<div style='font-size: 12px;'>Jl. Bojonegoro Blok B RT. 13 Simpang Pasir Palaran</div>";
    echo "<div style='font-size: 12px;'>HP: 0857-8727-5318</div>";
    echo "</div>";

    // Transaction Info
    echo "<div style='text-align: left; margin-bottom: 10px; border-bottom: 1px dashed #000; padding-bottom: 10px;'>";
    echo "<div style='display: flex; justify-content: space-between;'><span>Invoice:</span><span>{$sale['invoice_number']}</span></div>";
    echo "<div style='display: flex; justify-content: space-between;'><span>Tanggal:</span><span>" . formatDate($sale['sale_date']) . "</span></div>";
    echo "<div style='display: flex; justify-content: space-between;'><span>Waktu:</span><span>" . date('H:i:s', strtotime($sale['created_at'])) . "</span></div>";
    echo "<div style='display: flex; justify-content: space-between;'><span>Pelanggan:</span><span>{$sale['customer_name']}</span></div>";

    if ($sale['payment_status'] === 'partial') {
        echo "<div style='display: flex; justify-content: space-between;'><span>Status:</span><span>DP (Belum Lunas)</span></div>";
    }
    echo "</div>";

    // Sample Items
    echo "<div style='text-align: left; margin-bottom: 10px;'>";
    echo "<div style='font-weight: bold; margin-bottom: 5px;'>Sample Produk:</div>";
    echo "<div style='border-bottom: 1px dotted #ccc; padding-bottom: 3px; margin-bottom: 5px;'>";
    echo "<div style='font-weight: bold;'>Kusen Pintu Kayu Jati</div>";
    echo "<div style='display: flex; justify-content: space-between; font-size: 12px;'>";
    echo "<span>2 x Rp 200.000</span><span>Rp 400.000</span>";
    echo "</div></div>";

    echo "<div style='border-bottom: 1px dotted #ccc; padding-bottom: 3px; margin-bottom: 5px;'>";
    echo "<div style='font-weight: bold;'>Jendela Aluminium</div>";
    echo "<div style='display: flex; justify-content: space-between; font-size: 12px;'>";
    echo "<span>1 x Rp 150.000</span><span>Rp 150.000</span>";
    echo "</div></div>";
    echo "</div>";

    // Totals
    echo "<div style='border-top: 1px dashed #000; padding-top: 10px; text-align: left;'>";
    echo "<div style='display: flex; justify-content: space-between; margin-bottom: 3px;'>";
    echo "<span>Subtotal:</span><span>" . formatRupiah($sale['total_amount']) . "</span></div>";
    echo "<div style='display: flex; justify-content: space-between; font-weight: bold; font-size: 16px; border-top: 1px solid #000; padding-top: 5px; margin-top: 5px;'>";
    echo "<span>TOTAL:</span><span>" . formatRupiah($sale['total_amount']) . "</span></div>";

    if ($sale['payment_status'] === 'partial') {
        echo "<div style='margin-top: 10px; border-top: 1px dashed #000; padding-top: 5px;'>";
        echo "<div style='display: flex; justify-content: space-between;'>";
        echo "<span>Dibayar (DP):</span><span>" . formatRupiah($sale['paid_amount'] ?? 0) . "</span></div>";
        echo "<div style='display: flex; justify-content: space-between; font-weight: bold; color: red;'>";
        echo "<span>Sisa Bayar:</span><span>" . formatRupiah($sale['remaining_amount'] ?? 0) . "</span></div>";
        echo "</div>";
    }
    echo "</div>";

    // Footer
    echo "<div style='text-align: center; margin-top: 15px; border-top: 1px dashed #000; padding-top: 10px; font-size: 12px;'>";
    echo "<div>*** TERIMA KASIH ***</div>";
    echo "<div>Barang yang sudah dibeli tidak dapat dikembalikan</div>";
    echo "<div>Simpan struk ini sebagai bukti pembelian</div>";
    echo "<div style='margin-top: 10px;'>Dicetak: " . date('d/m/Y H:i:s') . "</div>";
    echo "</div>";

    echo "</div>";
    echo "</div>";

    echo "<h3>🔗 Test Links:</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='print_receipt.php?id={$sale['id']}' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🖨️ Test Print Receipt</a>";
    echo "<a href='../pages/sales.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📋 Riwayat Penjualan</a>";
    echo "<a href='../pages/pos.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛒 Form Penjualan</a>";
    echo "</div>";

    echo "<h3>✅ Perubahan yang Telah Dilakukan:</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<ul style='color: #155724; margin: 0;'>";
    echo "<li>✅ <strong>Nama Toko:</strong> TOKO KUSEN JAYA → <strong>UD. NADIA MEBEL</strong></li>";
    echo "<li>✅ <strong>Alamat:</strong> Jl. Raya Utama No. 123 → <strong>Jl. Bojonegoro Blok B RT. 13 Simpang Pasir Palaran</strong></li>";
    echo "<li>✅ <strong>HP:</strong> Ditambahkan <strong>HP: 0857-8727-5318</strong></li>";
    echo "<li>✅ <strong>Kasir:</strong> Dihapus dari struk</li>";
    echo "<li>✅ <strong>Telepon Lama:</strong> Dihapus (Telp: (021) 1234-5678)</li>";
    echo "<li>✅ <strong>Email:</strong> Dihapus (Email: <EMAIL>)</li>";
    echo "<li>✅ <strong>Title Page:</strong> Updated untuk konsistensi</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24;'>❌ Error:</h4>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}
h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
th {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}
</style>
