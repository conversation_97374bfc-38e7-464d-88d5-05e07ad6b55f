<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON data');
    }

    $customerId = $input['customer_id'] ?? null;
    $customerName = $input['customer_name'] ?? 'Walk-in';
    $items = $input['items'] ?? [];
    $paymentAmount = floatval($input['payment_amount'] ?? 0);
    $paymentType = $input['payment_type'] ?? 'full';
    $totalAmount = floatval($input['total_amount'] ?? 0);
    $notes = $input['notes'] ?? '';

    if (empty($items)) {
        throw new Exception('Tidak ada item dalam transaksi');
    }

    // Calculate totals
    $calculatedTotal = 0;
    $totalCost = 0;

    foreach ($items as $item) {
        $calculatedTotal += $item['total'];
        $totalCost += ($item['cost'] * $item['quantity']);
    }

    // Use the total from frontend (already calculated with discounts)
    $totalAmount = $calculatedTotal;
    $profit = $totalAmount - $totalCost;

    // Determine payment status
    if ($paymentType === 'dp') {
        $paymentStatus = 'partial';
        $remainingAmount = $totalAmount - $paymentAmount;

        // DP validation - allow any amount >= 0
        if ($paymentAmount < 0) {
            throw new Exception('Jumlah DP tidak valid');
        }
    } else {
        $paymentStatus = 'paid';
        $remainingAmount = 0;

        // Full payment validation
        if ($paymentAmount < $totalAmount) {
            throw new Exception('Jumlah pembayaran kurang');
        }
    }

    $db->beginTransaction();

    // Generate invoice number
    $invoiceNumber = generateInvoiceNumber();

    // Insert sale header
    $saleId = $db->insert('sales', [
        'invoice_number' => $invoiceNumber,
        'customer_id' => $customerId,
        'customer_name' => $customerName,
        'sale_date' => date('Y-m-d'),
        'total_amount' => $totalAmount,
        'total_cost' => $totalCost,
        'profit' => $profit,
        'payment_status' => $paymentStatus,
        'paid_amount' => $paymentAmount,
        'remaining_amount' => $remainingAmount,
        'notes' => $notes,
        'created_by' => 'POS System'
    ]);

    // Insert sale items and update stock
    foreach ($items as $item) {
        // Calculate values with discount
        $discountAmount = isset($item['discount_amount']) ? floatval($item['discount_amount']) : 0;
        $finalPrice = $item['price'] - $discountAmount;
        $totalPrice = $finalPrice * $item['quantity'];
        $totalCost = $item['cost'] * $item['quantity'];
        $profit = $totalPrice - $totalCost;

        // Insert sale item
        $db->insert('sale_items', [
            'sale_id' => $saleId,
            'product_id' => $item['product_id'],
            'product_code' => $item['code'],
            'product_name' => $item['name'],
            'quantity' => $item['quantity'],
            'unit_price' => $item['price'],
            'unit_cost' => $item['cost'],
            'discount_amount' => $discountAmount,
            'final_price' => $finalPrice,
            'total_price' => $totalPrice,
            'total_cost' => $totalCost,
            'profit' => $profit
        ]);

        // Update product stock
        $db->query(
            "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?",
            [$item['quantity'], $item['product_id']]
        );

        // Check if stock becomes negative
        $product = $db->fetchOne("SELECT stock_quantity FROM products WHERE id = ?", [$item['product_id']]);
        if ($product['stock_quantity'] < 0) {
            throw new Exception("Stok produk {$item['name']} tidak mencukupi");
        }
    }

    // Insert payment record
    $paymentTypeRecord = $paymentType === 'dp' ? 'dp' : 'full';
    $db->insert('sale_payments', [
        'sale_id' => $saleId,
        'payment_date' => date('Y-m-d'),
        'amount' => $paymentAmount,
        'payment_type' => $paymentTypeRecord,
        'notes' => $notes
    ]);

    $db->commit();

    $responseMessage = $paymentStatus === 'partial'
        ? 'Transaksi DP berhasil disimpan'
        : 'Transaksi berhasil disimpan';

    jsonResponse(true, $responseMessage, [
        'sale_id' => $saleId,
        'invoice_number' => $invoiceNumber,
        'total_amount' => $totalAmount,
        'payment_amount' => $paymentAmount,
        'payment_status' => $paymentStatus,
        'remaining_amount' => $remainingAmount,
        'change' => $paymentStatus === 'paid' ? ($paymentAmount - $totalAmount) : 0
    ]);

} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
