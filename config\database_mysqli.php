<?php
/**
 * Database Configuration using MySQLi (Alternative to PDO)
 */

class DatabaseMySQLi {
    private $host = 'localhost';
    private $username = 'root';
    private $password = '8@n90N3!';
    private $database = 'adi';
    private $connection;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $this->connection = new mysqli($this->host, $this->username, $this->password, $this->database);
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
            
            $this->connection->set_charset("utf8mb4");
        } catch (Exception $e) {
            die("Connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . $this->connection->error);
                }
                
                // Build type string for bind_param
                $types = '';
                foreach ($params as $param) {
                    if (is_int($param)) {
                        $types .= 'i';
                    } elseif (is_float($param)) {
                        $types .= 'd';
                    } else {
                        $types .= 's';
                    }
                }
                
                $stmt->bind_param($types, ...$params);
                $stmt->execute();
                
                return $stmt;
            } else {
                $result = $this->connection->query($sql);
                if (!$result) {
                    throw new Exception("Query failed: " . $this->connection->error);
                }
                return $result;
            }
        } catch (Exception $e) {
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }
    
    public function fetchAll($sql, $params = []) {
        $result = $this->query($sql, $params);
        
        if ($result instanceof mysqli_stmt) {
            $data = $result->get_result()->fetch_all(MYSQLI_ASSOC);
            $result->close();
            return $data;
        } else {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
    }
    
    public function fetchOne($sql, $params = []) {
        $result = $this->query($sql, $params);
        
        if ($result instanceof mysqli_stmt) {
            $data = $result->get_result()->fetch_assoc();
            $result->close();
            return $data;
        } else {
            return $result->fetch_assoc();
        }
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = str_repeat('?,', count($data) - 1) . '?';
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, array_values($data));
        
        $insertId = $this->connection->insert_id;
        if ($stmt instanceof mysqli_stmt) {
            $stmt->close();
        }
        
        return $insertId;
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = ?";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge(array_values($data), $whereParams);
        
        $stmt = $this->query($sql, $params);
        $affectedRows = $this->connection->affected_rows;
        
        if ($stmt instanceof mysqli_stmt) {
            $stmt->close();
        }
        
        return $affectedRows;
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        
        $affectedRows = $this->connection->affected_rows;
        
        if ($stmt instanceof mysqli_stmt) {
            $stmt->close();
        }
        
        return $affectedRows;
    }
    
    public function beginTransaction() {
        return $this->connection->begin_transaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function escape($string) {
        return $this->connection->real_escape_string($string);
    }
}

// Global database instance
$db = new DatabaseMySQLi();
?>
