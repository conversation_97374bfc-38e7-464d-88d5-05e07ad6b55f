<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

try {
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $type = $_GET['type'] ?? 'summary';

    if (empty($dateFrom) || empty($dateTo)) {
        throw new Exception('Rentang tanggal diperlukan');
    }

    // Get summary data
    $summary = $db->fetchOne("
        SELECT
            COUNT(*) as total_transactions,
            COALESCE(SUM(total_amount), 0) as total_sales,
            COALESCE(SUM(total_cost), 0) as total_cost,
            COALESCE(SUM(profit), 0) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
    ", [$dateFrom, $dateTo]);

    $profitMargin = $summary['total_cost'] > 0
        ? round(($summary['total_profit'] / $summary['total_cost']) * 100, 1)
        : 0;

    $response = [
        'success' => true,
        'data' => [
            'summary' => [
                'period' => formatDate($dateFrom) . ' - ' . formatDate($dateTo),
                'generated' => date('d/m/Y H:i:s'),
                'total_transactions' => $summary['total_transactions'],
                'total_sales' => $summary['total_sales'],
                'total_cost' => $summary['total_cost'],
                'total_profit' => $summary['total_profit'],
                'profit_margin' => $profitMargin
            ]
        ]
    ];

    // Generate specific report data
    switch ($type) {
        case 'summary':
            $response['data']['details'] = getSummaryData($dateFrom, $dateTo);
            break;
        case 'daily':
            $response['data']['details'] = getDailyData($dateFrom, $dateTo);
            break;
        case 'product':
            $response['data']['details'] = getProductData($dateFrom, $dateTo);
            break;
        case 'customer':
            $response['data']['details'] = getCustomerData($dateFrom, $dateTo);
            break;
    }

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function getSummaryData($dateFrom, $dateTo) {
    global $db;

    // Top Products
    $topProducts = $db->fetchAll("
        SELECT
            si.product_name,
            SUM(si.quantity) as total_qty,
            SUM(si.total_price) as total_sales,
            SUM(si.profit) as total_profit
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE s.sale_date BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_name
        ORDER BY total_sales DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    // Top Customers
    $topCustomers = $db->fetchAll("
        SELECT
            customer_name,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_purchases
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY customer_id, customer_name
        ORDER BY total_purchases DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    return [
        'top_products' => $topProducts,
        'top_customers' => $topCustomers
    ];
}

function getDailyData($dateFrom, $dateTo) {
    global $db;

    $dailyData = $db->fetchAll("
        SELECT
            sale_date,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            SUM(profit) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY sale_date
        ORDER BY sale_date
    ", [$dateFrom, $dateTo]);

    return $dailyData;
}

function getProductData($dateFrom, $dateTo) {
    global $db;

    $productData = $db->fetchAll("
        SELECT
            si.product_code,
            si.product_name,
            SUM(si.quantity) as total_qty,
            SUM(si.total_price) as total_sales,
            SUM(si.total_cost) as total_cost,
            SUM(si.profit) as total_profit,
            AVG(si.unit_price) as avg_price
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE s.sale_date BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_code, si.product_name
        ORDER BY total_sales DESC
    ", [$dateFrom, $dateTo]);

    return $productData;
}

function getCustomerData($dateFrom, $dateTo) {
    global $db;

    $customerData = $db->fetchAll("
        SELECT
            customer_name,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_purchases,
            SUM(profit) as total_profit,
            AVG(total_amount) as avg_transaction
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY customer_id, customer_name
        ORDER BY total_purchases DESC
    ", [$dateFrom, $dateTo]);

    return $customerData;
}
?>
