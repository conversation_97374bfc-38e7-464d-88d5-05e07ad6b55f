<?php
/**
 * Check PHP Configuration and Extensions
 */

echo "<h2>PHP Configuration Check</h2>";

echo "<h3>1. PHP Version</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "PHP SAPI: " . php_sapi_name() . "<br><br>";

echo "<h3>2. Required Extensions</h3>";

$required_extensions = [
    'mysqli' => 'MySQLi Extension',
    'json' => 'JSON Support',
    'mbstring' => 'Multibyte String',
    'openssl' => 'OpenSSL',
    'session' => 'Session Support'
];

foreach ($required_extensions as $ext => $description) {
    if (extension_loaded($ext)) {
        echo "✅ <strong>$description</strong> - Loaded<br>";
    } else {
        echo "❌ <strong>$description</strong> - NOT LOADED<br>";
    }
}

echo "<h3>3. Database Connection Test</h3>";

// Test basic MySQL connection
try {
    if (extension_loaded('mysqli')) {
        echo "✅ MySQLi extension is loaded<br>";

        // Try to connect
        $host = 'localhost';
        $username = 'root';
        $password = '8@n90N3!';

        $mysqli = new mysqli($host, $username, $password);
        if ($mysqli->connect_error) {
            throw new Exception("Connection failed: " . $mysqli->connect_error);
        }
        $mysqli->set_charset("utf8mb4");

        echo "✅ Database connection successful!<br>";

        // Check MySQL version
        $result = $mysqli->query('SELECT VERSION()');
        $version = $result->fetch_row()[0];
        echo "MySQL Version: $version<br>";

        $mysqli->close();

    } else {
        echo "❌ MySQLi extension is NOT loaded<br>";
        echo "<strong>Solution:</strong><br>";
        echo "1. Edit php.ini file<br>";
        echo "2. Uncomment: extension=mysqli<br>";
        echo "3. Restart AppServ<br>";
    }

} catch (mysqli_sql_exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    echo "<strong>Possible solutions:</strong><br>";
    echo "1. Check if MySQL/MariaDB is running<br>";
    echo "2. Verify database credentials<br>";
    echo "3. Check if port 3306 is available<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    echo "<strong>Possible solutions:</strong><br>";
    echo "1. Check if MySQL/MariaDB is running<br>";
    echo "2. Verify database credentials<br>";
    echo "3. Check if port 3306 is available<br>";
}

echo "<h3>4. PHP Configuration</h3>";
echo "PHP Configuration File: " . php_ini_loaded_file() . "<br>";
echo "Additional .ini files: " . php_ini_scanned_files() . "<br>";

echo "<h3>5. All Loaded Extensions</h3>";
$extensions = get_loaded_extensions();
sort($extensions);

echo "<div style='columns: 3; column-gap: 20px;'>";
foreach ($extensions as $ext) {
    echo "• $ext<br>";
}
echo "</div>";

echo "<h3>6. MySQL/MariaDB Service Check</h3>";

// Check if MySQL service is running (Windows)
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    echo "<p>Checking MySQL service status...</p>";

    $services = ['MySQL', 'MariaDB', 'mysql', 'mariadb'];
    $found = false;

    foreach ($services as $service) {
        $output = shell_exec("sc query $service 2>nul");
        if ($output && strpos($output, 'RUNNING') !== false) {
            echo "✅ $service service is running<br>";
            $found = true;
        }
    }

    if (!$found) {
        echo "❌ No MySQL/MariaDB service found running<br>";
        echo "<strong>Please start MySQL/MariaDB service from AppServ control panel</strong><br>";
    }
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>If MySQLi is not loaded, edit php.ini and uncomment extension=mysqli</li>";
echo "<li>Restart AppServ completely</li>";
echo "<li>Run this check again</li>";
echo "<li>Then try <a href='test_database.php'>test_database.php</a></li>";
echo "</ol>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
.success { color: green; }
.error { color: red; }
</style>
