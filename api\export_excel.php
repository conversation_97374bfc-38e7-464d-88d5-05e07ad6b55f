<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $type = $_GET['type'] ?? 'summary';

    if (empty($dateFrom) || empty($dateTo)) {
        throw new Exception('Rentang tanggal diperlukan');
    }

    // Set headers for Excel download
    $filename = 'Laporan_' . ucfirst($type) . '_' . date('Y-m-d_H-i-s') . '.xlsx';
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // Start HTML table (Excel will interpret this as spreadsheet)
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
    echo '<!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Laporan</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->';
    echo '<style>';
    echo '.header { background-color: #4472C4; color: white; font-weight: bold; text-align: center; }';
    echo '.title { font-weight: bold; font-size: 14px; }';
    echo '.currency { text-align: right; }';
    echo '.center { text-align: center; }';
    echo 'table { border-collapse: collapse; width: 100%; }';
    echo 'td, th { border: 1px solid #ddd; padding: 8px; }';
    echo '</style>';
    echo '</head>';
    echo '<body>';
    echo '<table>';

    // Get summary data
    $summary = $db->fetchOne("
        SELECT
            COUNT(*) as total_transactions,
            COALESCE(SUM(total_amount), 0) as total_sales,
            COALESCE(SUM(total_cost), 0) as total_cost,
            COALESCE(SUM(profit), 0) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
    ", [$dateFrom, $dateTo]);

    $profitMargin = $summary['total_cost'] > 0
        ? round(($summary['total_profit'] / $summary['total_cost']) * 100, 1)
        : 0;

    // Title and header info
    echo '<tr><td colspan="10" class="title">LAPORAN PENJUALAN</td></tr>';
    echo '<tr><td colspan="10">Periode: ' . formatDate($dateFrom) . ' - ' . formatDate($dateTo) . '</td></tr>';
    echo '<tr><td colspan="10">Dicetak: ' . date('d/m/Y H:i:s') . '</td></tr>';
    echo '<tr><td colspan="10">&nbsp;</td></tr>'; // Empty row

    // Summary
    echo '<tr><td colspan="10" class="header">RINGKASAN PENJUALAN</td></tr>';
    echo '<tr><td>Total Transaksi</td><td>' . number_format($summary['total_transactions']) . ' transaksi</td><td colspan="8">&nbsp;</td></tr>';
    echo '<tr><td>Total Penjualan</td><td class="currency">Rp ' . number_format($summary['total_sales'], 0, ',', '.') . '</td><td colspan="8">&nbsp;</td></tr>';
    echo '<tr><td>Total HPP</td><td class="currency">Rp ' . number_format($summary['total_cost'], 0, ',', '.') . '</td><td colspan="8">&nbsp;</td></tr>';
    echo '<tr><td>Total Laba</td><td class="currency">Rp ' . number_format($summary['total_profit'], 0, ',', '.') . '</td><td colspan="8">&nbsp;</td></tr>';
    echo '<tr><td>Margin Laba</td><td>' . $profitMargin . '%</td><td colspan="8">&nbsp;</td></tr>';
    echo '<tr><td colspan="10">&nbsp;</td></tr>'; // Empty row

    // Generate specific report based on type
    switch ($type) {
        case 'summary':
            generateSummaryHTML($dateFrom, $dateTo);
            break;
        case 'daily':
            generateDailyHTML($dateFrom, $dateTo);
            break;
        case 'product':
            generateProductHTML($dateFrom, $dateTo);
            break;
        case 'customer':
            generateCustomerHTML($dateFrom, $dateTo);
            break;
    }

    // Close HTML table
    echo '</table>';
    echo '</body>';
    echo '</html>';

} catch (Exception $e) {
    header('Content-Type: text/html');
    die('Error: ' . $e->getMessage());
}

function generateSummaryHTML($dateFrom, $dateTo) {
    global $db;

    // Top Products
    echo '<tr><td colspan="10" class="header">PRODUK TERLARIS</td></tr>';
    echo '<tr class="header"><td>No</td><td>Nama Produk</td><td>Qty Terjual</td><td>Total Penjualan</td><td>Total Laba</td><td colspan="5">&nbsp;</td></tr>';

    $topProducts = $db->fetchAll("
        SELECT
            si.product_name,
            SUM(si.quantity) as total_qty,
            SUM(si.total_price) as total_sales,
            SUM(si.profit) as total_profit
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE s.sale_date BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_name
        ORDER BY total_sales DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    $no = 1;
    foreach ($topProducts as $product) {
        echo '<tr>';
        echo '<td class="center">' . $no++ . '</td>';
        echo '<td>' . htmlspecialchars($product['product_name']) . '</td>';
        echo '<td class="center">' . number_format($product['total_qty']) . '</td>';
        echo '<td class="currency">Rp ' . number_format($product['total_sales'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($product['total_profit'], 0, ',', '.') . '</td>';
        echo '<td colspan="5">&nbsp;</td>';
        echo '</tr>';
    }

    echo '<tr><td colspan="10">&nbsp;</td></tr>'; // Empty row

    // Top Customers
    echo '<tr><td colspan="10" class="header">PELANGGAN TERBAIK</td></tr>';
    echo '<tr class="header"><td>No</td><td>Nama Pelanggan</td><td>Total Transaksi</td><td>Total Pembelian</td><td colspan="6">&nbsp;</td></tr>';

    $topCustomers = $db->fetchAll("
        SELECT
            customer_name,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_purchases
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY customer_id, customer_name
        ORDER BY total_purchases DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    $no = 1;
    foreach ($topCustomers as $customer) {
        echo '<tr>';
        echo '<td class="center">' . $no++ . '</td>';
        echo '<td>' . htmlspecialchars($customer['customer_name']) . '</td>';
        echo '<td class="center">' . number_format($customer['total_transactions']) . '</td>';
        echo '<td class="currency">Rp ' . number_format($customer['total_purchases'], 0, ',', '.') . '</td>';
        echo '<td colspan="6">&nbsp;</td>';
        echo '</tr>';
    }
}

function generateDailyHTML($dateFrom, $dateTo) {
    global $db;

    echo '<tr><td colspan="10" class="header">LAPORAN PENJUALAN HARIAN</td></tr>';
    echo '<tr class="header"><td>Tanggal</td><td>Total Transaksi</td><td>Total Penjualan</td><td>Total Laba</td><td>Margin (%)</td><td colspan="5">&nbsp;</td></tr>';

    $dailyData = $db->fetchAll("
        SELECT
            sale_date,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            SUM(profit) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY sale_date
        ORDER BY sale_date
    ", [$dateFrom, $dateTo]);

    $totalTransactions = 0;
    $totalSales = 0;
    $totalProfit = 0;

    foreach ($dailyData as $day) {
        $margin = $day['total_sales'] > 0
            ? round(($day['total_profit'] / $day['total_sales']) * 100, 1)
            : 0;

        $totalTransactions += $day['total_transactions'];
        $totalSales += $day['total_sales'];
        $totalProfit += $day['total_profit'];

        echo '<tr>';
        echo '<td class="center">' . formatDate($day['sale_date']) . '</td>';
        echo '<td class="center">' . number_format($day['total_transactions']) . '</td>';
        echo '<td class="currency">Rp ' . number_format($day['total_sales'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($day['total_profit'], 0, ',', '.') . '</td>';
        echo '<td class="center">' . $margin . '%</td>';
        echo '<td colspan="5">&nbsp;</td>';
        echo '</tr>';
    }

    // Total row
    $totalMargin = $totalSales > 0 ? round(($totalProfit / $totalSales) * 100, 1) : 0;
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td class="center">TOTAL</td>';
    echo '<td class="center">' . number_format($totalTransactions) . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalSales, 0, ',', '.') . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalProfit, 0, ',', '.') . '</td>';
    echo '<td class="center">' . $totalMargin . '%</td>';
    echo '<td colspan="5">&nbsp;</td>';
    echo '</tr>';
}

function generateProductHTML($dateFrom, $dateTo) {
    global $db;

    echo '<tr><td colspan="10" class="header">LAPORAN PENJUALAN PER PRODUK</td></tr>';
    echo '<tr class="header"><td>No</td><td>Kode</td><td>Nama Produk</td><td>Qty</td><td>Rata-rata Harga</td><td>Total Penjualan</td><td>Total HPP</td><td>Laba</td><td>Margin (%)</td><td>&nbsp;</td></tr>';

    $productData = $db->fetchAll("
        SELECT
            si.product_code,
            si.product_name,
            SUM(si.quantity) as total_qty,
            SUM(si.total_price) as total_sales,
            SUM(si.total_cost) as total_cost,
            SUM(si.profit) as total_profit,
            AVG(si.unit_price) as avg_price
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE s.sale_date BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_code, si.product_name
        ORDER BY total_sales DESC
    ", [$dateFrom, $dateTo]);

    $no = 1;
    $totalQty = 0;
    $totalSales = 0;
    $totalCost = 0;
    $totalProfit = 0;

    foreach ($productData as $product) {
        $margin = $product['total_cost'] > 0
            ? round(($product['total_profit'] / $product['total_cost']) * 100, 1)
            : 0;

        $totalQty += $product['total_qty'];
        $totalSales += $product['total_sales'];
        $totalCost += $product['total_cost'];
        $totalProfit += $product['total_profit'];

        echo '<tr>';
        echo '<td class="center">' . $no++ . '</td>';
        echo '<td class="center">' . htmlspecialchars($product['product_code']) . '</td>';
        echo '<td>' . htmlspecialchars($product['product_name']) . '</td>';
        echo '<td class="center">' . number_format($product['total_qty']) . '</td>';
        echo '<td class="currency">Rp ' . number_format($product['avg_price'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($product['total_sales'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($product['total_cost'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($product['total_profit'], 0, ',', '.') . '</td>';
        echo '<td class="center">' . $margin . '%</td>';
        echo '<td>&nbsp;</td>';
        echo '</tr>';
    }

    // Total row
    $totalMargin = $totalCost > 0 ? round(($totalProfit / $totalCost) * 100, 1) : 0;
    $avgPrice = $totalQty > 0 ? $totalSales / $totalQty : 0;
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td>&nbsp;</td>';
    echo '<td>&nbsp;</td>';
    echo '<td class="center">TOTAL</td>';
    echo '<td class="center">' . number_format($totalQty) . '</td>';
    echo '<td class="currency">Rp ' . number_format($avgPrice, 0, ',', '.') . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalSales, 0, ',', '.') . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalCost, 0, ',', '.') . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalProfit, 0, ',', '.') . '</td>';
    echo '<td class="center">' . $totalMargin . '%</td>';
    echo '<td>&nbsp;</td>';
    echo '</tr>';
}

function generateCustomerHTML($dateFrom, $dateTo) {
    global $db;

    echo '<tr><td colspan="10" class="header">LAPORAN PENJUALAN PER PELANGGAN</td></tr>';
    echo '<tr class="header"><td>No</td><td>Nama Pelanggan</td><td>Total Transaksi</td><td>Total Pembelian</td><td>Rata-rata Transaksi</td><td>Total Laba</td><td>Kontribusi (%)</td><td colspan="3">&nbsp;</td></tr>';

    $customerData = $db->fetchAll("
        SELECT
            customer_name,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_purchases,
            SUM(profit) as total_profit,
            AVG(total_amount) as avg_transaction
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY customer_id, customer_name
        ORDER BY total_purchases DESC
    ", [$dateFrom, $dateTo]);

    $no = 1;
    $totalTransactions = 0;
    $totalPurchases = 0;
    $totalProfit = 0;
    $grandTotal = array_sum(array_column($customerData, 'total_purchases'));

    foreach ($customerData as $customer) {
        $contribution = $grandTotal > 0 ? round(($customer['total_purchases'] / $grandTotal) * 100, 1) : 0;

        $totalTransactions += $customer['total_transactions'];
        $totalPurchases += $customer['total_purchases'];
        $totalProfit += $customer['total_profit'];

        echo '<tr>';
        echo '<td class="center">' . $no++ . '</td>';
        echo '<td>' . htmlspecialchars($customer['customer_name']) . '</td>';
        echo '<td class="center">' . number_format($customer['total_transactions']) . '</td>';
        echo '<td class="currency">Rp ' . number_format($customer['total_purchases'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($customer['avg_transaction'], 0, ',', '.') . '</td>';
        echo '<td class="currency">Rp ' . number_format($customer['total_profit'], 0, ',', '.') . '</td>';
        echo '<td class="center">' . $contribution . '%</td>';
        echo '<td colspan="3">&nbsp;</td>';
        echo '</tr>';
    }

    // Total row
    $avgTransaction = $totalTransactions > 0 ? $totalPurchases / $totalTransactions : 0;
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td>&nbsp;</td>';
    echo '<td class="center">TOTAL</td>';
    echo '<td class="center">' . number_format($totalTransactions) . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalPurchases, 0, ',', '.') . '</td>';
    echo '<td class="currency">Rp ' . number_format($avgTransaction, 0, ',', '.') . '</td>';
    echo '<td class="currency">Rp ' . number_format($totalProfit, 0, ',', '.') . '</td>';
    echo '<td class="center">100%</td>';
    echo '<td colspan="3">&nbsp;</td>';
    echo '</tr>';
}
?>
