<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $type = $_GET['type'] ?? 'summary';

    if (empty($dateFrom) || empty($dateTo)) {
        throw new Exception('Rentang tanggal diperlukan');
    }

    // Get summary data
    $summary = $db->fetchOne("
        SELECT
            COUNT(*) as total_transactions,
            COALESCE(SUM(total_amount), 0) as total_sales,
            COALESCE(SUM(total_cost), 0) as total_cost,
            COALESCE(SUM(profit), 0) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
    ", [$dateFrom, $dateTo]);

    $profitMargin = $summary['total_cost'] > 0
        ? round(($summary['total_profit'] / $summary['total_cost']) * 100, 1)
        : 0;

    $reportTitle = '';
    switch ($type) {
        case 'summary':
            $reportTitle = 'Laporan <PERSON> Penjualan';
            break;
        case 'daily':
            $reportTitle = 'Laporan Penjualan Harian';
            break;
        case 'product':
            $reportTitle = 'Laporan Penjualan Per Produk';
            break;
        case 'customer':
            $reportTitle = 'Laporan Penjualan Per Pelanggan';
            break;
    }

} catch (Exception $e) {
    die('Error: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $reportTitle; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 12px;
            margin-bottom: 2px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0 10px 0;
        }
        .report-period {
            font-size: 12px;
            margin-bottom: 20px;
        }
        .summary-section {
            margin-bottom: 30px;
        }
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            background-color: #f0f0f0;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .summary-table th,
        .summary-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .summary-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .summary-table .number {
            text-align: right;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        .data-table th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        .data-table .number {
            text-align: right;
        }
        .data-table .center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            font-size: 10px;
            text-align: center;
        }
        .section-break {
            page-break-before: always;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .report-container { max-width: 100%; margin: 0; padding: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">TOKO KUSEN JAYA</div>
            <div class="company-info">Jl. Raya Utama No. 123</div>
            <div class="company-info">Telp: (021) 1234-5678 | Email: <EMAIL></div>
        </div>

        <!-- Report Title -->
        <div class="report-title"><?php echo strtoupper($reportTitle); ?></div>
        <div class="report-period">
            Periode: <?php echo formatDate($dateFrom); ?> s/d <?php echo formatDate($dateTo); ?>
        </div>

        <!-- Summary Section -->
        <div class="summary-section">
            <div class="summary-title">RINGKASAN PENJUALAN</div>
            <table class="summary-table">
                <tr>
                    <td><strong>Total Transaksi</strong></td>
                    <td class="number"><?php echo number_format($summary['total_transactions']); ?> transaksi</td>
                </tr>
                <tr>
                    <td><strong>Total Penjualan</strong></td>
                    <td class="number"><?php echo formatRupiah($summary['total_sales']); ?></td>
                </tr>
                <tr>
                    <td><strong>Total HPP</strong></td>
                    <td class="number"><?php echo formatRupiah($summary['total_cost']); ?></td>
                </tr>
                <tr>
                    <td><strong>Total Laba</strong></td>
                    <td class="number"><?php echo formatRupiah($summary['total_profit']); ?></td>
                </tr>
                <tr>
                    <td><strong>Margin Laba</strong></td>
                    <td class="number"><?php echo $profitMargin; ?>%</td>
                </tr>
            </table>
        </div>

        <!-- Detail Report -->
        <?php
        switch ($type) {
            case 'summary':
                include 'print_summary_detail.php';
                break;
            case 'daily':
                include 'print_daily_detail.php';
                break;
            case 'product':
                include 'print_product_detail.php';
                break;
            case 'customer':
                include 'print_customer_detail.php';
                break;
        }
        ?>

        <!-- Footer -->
        <div class="footer">
            <div>Dicetak pada: <?php echo date('d/m/Y H:i:s'); ?></div>
            <div>Sistem POS - Toko Kusen Jaya</div>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }

        // Close window after printing
        window.onafterprint = function() {
            window.close();
        }
    </script>
</body>
</html>
