<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_POST['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID produk diperlukan');
    }
    
    // Check if product exists
    $product = $db->fetchOne("SELECT id, name FROM products WHERE id = ?", [$id]);
    if (!$product) {
        throw new Exception('Produk tidak ditemukan');
    }
    
    // Check if product is used in sales
    $usedInSales = $db->fetchOne(
        "SELECT COUNT(*) as total FROM sale_items WHERE product_id = ?", 
        [$id]
    )['total'];
    
    if ($usedInSales > 0) {
        throw new Exception('Produk tidak dapat dihapus karena sudah digunakan dalam transaksi penjualan');
    }
    
    $db->beginTransaction();
    
    // Delete the product
    $db->delete('products', 'id = ?', [$id]);
    
    $db->commit();
    
    jsonResponse(true, 'Produk "' . $product['name'] . '" berhasil dihapus');
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
