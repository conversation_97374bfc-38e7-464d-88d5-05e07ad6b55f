<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validate required fields
    if (empty($_POST['name'])) {
        throw new Exception('Nama kategori diperlukan');
    }
    
    $id = $_POST['id'] ?? null;
    $name = sanitizeInput($_POST['name']);
    $description = sanitizeInput($_POST['description'] ?? '');
    
    $db->beginTransaction();
    
    if ($id) {
        // Update existing category
        
        // Check if name is unique (excluding current category)
        $existing = $db->fetchOne(
            "SELECT id FROM categories WHERE name = ? AND id != ?", 
            [$name, $id]
        );
        if ($existing) {
            throw new Exception('Nama kategori sudah digunakan');
        }
        
        // Check if category exists
        $category = $db->fetchOne("SELECT id FROM categories WHERE id = ?", [$id]);
        if (!$category) {
            throw new Exception('Kategori tidak ditemukan');
        }
        
        $db->update('categories', [
            'name' => $name,
            'description' => $description,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$id]);
        
        $message = 'Kategori berhasil diperbarui';
        
    } else {
        // Create new category
        
        // Check if name is unique
        $existing = $db->fetchOne("SELECT id FROM categories WHERE name = ?", [$name]);
        if ($existing) {
            throw new Exception('Nama kategori sudah digunakan');
        }
        
        $id = $db->insert('categories', [
            'name' => $name,
            'description' => $description
        ]);
        
        $message = 'Kategori berhasil ditambahkan';
    }
    
    $db->commit();
    
    jsonResponse(true, $message, ['id' => $id]);
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
