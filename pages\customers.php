<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Data Pelanggan';
$currentPage = 'customers';
$pageIcon = 'fas fa-users';
$breadcrumb = [
    ['title' => 'Master Data', 'url' => '#'],
    ['title' => 'Data Pelanggan']
];

include '../includes/header.php';
?>

<div class="card shadow">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-white">
            <i class="fas fa-users me-2"></i>
            Data Pelanggan
        </h6>
        <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#customerModal">
            <i class="fas fa-plus me-1"></i>
            <PERSON>bah Pelanggan
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="customersTable" class="table table-striped table-hover" style="width:100%">
                <thead>
                    <tr>
                        <th>Kode</th>
                        <th>Nama Pelanggan</th>
                        <th>Telepon</th>
                        <th>Email</th>
                        <th>Total Pembelian</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Customer Modal -->
<div class="modal fade" id="customerModal" tabindex="-1" aria-labelledby="customerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalLabel">Tambah Pelanggan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="customerForm">
                <div class="modal-body">
                    <input type="hidden" id="customerId" name="id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerCode" class="form-label">Kode Pelanggan *</label>
                                <input type="text" class="form-control" id="customerCode" name="code" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerName" class="form-label">Nama Pelanggan *</label>
                                <input type="text" class="form-control" id="customerName" name="name" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerPhone" class="form-label">Telepon</label>
                                <input type="text" class="form-control" id="customerPhone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="customerEmail" name="email">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">Alamat</label>
                        <textarea class="form-control" id="customerAddress" name="address" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                            <label class="form-check-label" for="isActive">
                                Pelanggan Aktif
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Customer Detail Modal -->
<div class="modal fade" id="customerDetailModal" tabindex="-1" aria-labelledby="customerDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerDetailModalLabel">Detail Pelanggan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="customerDetailContent">
                <!-- Content will be loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $("#customersTable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "../api/customers.php",
            type: "POST"
        },
        columns: [
            { data: 0, width: "10%" },
            { data: 1, width: "25%" },
            { data: 2, width: "12%" },
            { data: 3, width: "15%" },
            { data: 4, width: "15%" },
            { data: 5, width: "8%" },
            { data: 6, width: "15%", orderable: false }
        ],
        order: [[1, "asc"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true
    });

    // Generate customer code
    function generateCustomerCode() {
        var code = "CUST" + Math.floor(Math.random() * 9000 + 1000);
        $("#customerCode").val(code);
    }

    // Add Customer
    $("#customerForm").on("submit", function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        formData.set("is_active", $("#isActive").is(":checked") ? 1 : 0);

        $.ajax({
            url: "../api/customer_save.php",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $("#customerModal").modal("hide");
                    table.ajax.reload();
                    showSuccess(response.message);
                } else {
                    showError(response.message);
                }
            },
            error: function() {
                showError("Terjadi kesalahan saat menyimpan data");
            }
        });
    });

    // Edit Customer
    $(document).on("click", ".btn-edit", function() {
        var id = $(this).data("id");

        $.ajax({
            url: "../api/customer_get.php",
            type: "GET",
            data: { id: id },
            success: function(response) {
                if (response.success) {
                    var customer = response.data;

                    $("#customerId").val(customer.id);
                    $("#customerCode").val(customer.code);
                    $("#customerName").val(customer.name);
                    $("#customerPhone").val(customer.phone);
                    $("#customerEmail").val(customer.email);
                    $("#customerAddress").val(customer.address);
                    $("#isActive").prop("checked", customer.is_active == 1);

                    $("#customerModalLabel").text("Edit Pelanggan");
                    $("#customerModal").modal("show");
                } else {
                    showError(response.message);
                }
            }
        });
    });

    // View Customer Detail
    $(document).on("click", ".btn-view", function() {
        var id = $(this).data("id");

        $.ajax({
            url: "../api/customer_detail.php",
            type: "GET",
            data: { id: id },
            success: function(response) {
                if (response.success) {
                    $("#customerDetailContent").html(response.data.html);
                    $("#customerDetailModal").modal("show");
                } else {
                    showError(response.message);
                }
            }
        });
    });

    // Delete Customer
    $(document).on("click", ".btn-delete", function() {
        var id = $(this).data("id");

        confirmDelete(function() {
            $.ajax({
                url: "../api/customer_delete.php",
                type: "POST",
                data: { id: id },
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload();
                        showSuccess(response.message);
                    } else {
                        showError(response.message);
                    }
                }
            });
        });
    });

    // Reset form when modal is hidden
    $("#customerModal").on("hidden.bs.modal", function() {
        $("#customerForm")[0].reset();
        $("#customerId").val("");
        $("#customerModalLabel").text("Tambah Pelanggan");
        generateCustomerCode();
    });

    // Generate code on modal show for new customer
    $("#customerModal").on("show.bs.modal", function() {
        if (!$("#customerId").val()) {
            generateCustomerCode();
        }
    });
});
</script>
';

include '../includes/footer.php';
?>
