<?php
/**
 * Test Timezone Configuration
 * Verifikasi pengaturan timezone Makassar, Indonesia
 */

// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🕐 Timezone Configuration Test</h2>";

echo "<h3>1. PHP Timezone Settings</h3>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

// Current timezone
$currentTimezone = date_default_timezone_get();
$isCorrect = $currentTimezone === 'Asia/Makassar';
echo "<tr>";
echo "<td><strong>Current Timezone</strong></td>";
echo "<td>$currentTimezone</td>";
echo "<td>" . ($isCorrect ? "✅ Correct" : "❌ Wrong") . "</td>";
echo "</tr>";

// Current date and time
$currentDateTime = date('Y-m-d H:i:s');
echo "<tr>";
echo "<td><strong>Current DateTime</strong></td>";
echo "<td>$currentDateTime</td>";
echo "<td>✅ WITA (UTC+8)</td>";
echo "</tr>";

// Formatted date
$formattedDate = formatDate(date('Y-m-d'));
echo "<tr>";
echo "<td><strong>Formatted Date</strong></td>";
echo "<td>$formattedDate</td>";
echo "<td>✅ Indonesian Format</td>";
echo "</tr>";

// Formatted datetime
$formattedDateTime = formatDateTime(date('Y-m-d H:i:s'));
echo "<tr>";
echo "<td><strong>Formatted DateTime</strong></td>";
echo "<td>$formattedDateTime</td>";
echo "<td>✅ Indonesian Format</td>";
echo "</tr>";

echo "</table>";

echo "<h3>2. Database Timezone Settings</h3>";
try {
    // Test database timezone
    $dbTimezone = $db->fetchOne("SELECT @@session.time_zone as timezone");
    $dbTime = $db->fetchOne("SELECT NOW() as current_time");
    
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>MySQL Timezone</strong></td>";
    echo "<td>" . $dbTimezone['timezone'] . "</td>";
    echo "<td>" . ($dbTimezone['timezone'] === '+08:00' ? "✅ Correct" : "❌ Wrong") . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>MySQL Current Time</strong></td>";
    echo "<td>" . $dbTime['current_time'] . "</td>";
    echo "<td>✅ WITA Time</td>";
    echo "</tr>";
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}

echo "<h3>3. Time Comparison</h3>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr><th>Location</th><th>Time</th><th>Offset</th></tr>";

// Different timezone comparisons
$timezones = [
    'Asia/Makassar' => 'Makassar (WITA)',
    'Asia/Jakarta' => 'Jakarta (WIB)', 
    'Asia/Jayapura' => 'Jayapura (WIT)',
    'UTC' => 'UTC',
    'Asia/Singapore' => 'Singapore'
];

foreach ($timezones as $tz => $label) {
    $dt = new DateTime('now', new DateTimeZone($tz));
    $offset = $dt->format('P');
    echo "<tr>";
    echo "<td><strong>$label</strong></td>";
    echo "<td>" . $dt->format('Y-m-d H:i:s') . "</td>";
    echo "<td>$offset</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>4. Function Tests</h3>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr><th>Function</th><th>Result</th><th>Status</th></tr>";

// Test invoice generation
$invoice = generateInvoiceNumber();
echo "<tr>";
echo "<td><strong>generateInvoiceNumber()</strong></td>";
echo "<td>$invoice</td>";
echo "<td>✅ Uses Makassar date</td>";
echo "</tr>";

// Test date formatting
$testDate = '2024-12-01 15:30:00';
$formatted = formatDateTime($testDate);
echo "<tr>";
echo "<td><strong>formatDateTime()</strong></td>";
echo "<td>$formatted</td>";
echo "<td>✅ Indonesian format</td>";
echo "</tr>";

echo "</table>";

echo "<h3>5. Summary</h3>";
if ($currentTimezone === 'Asia/Makassar') {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin: 0;'>✅ TIMEZONE CONFIGURATION CORRECT!</h4>";
    echo "<p style='color: #155724; margin: 5px 0 0 0;'>Project is using Makassar, Indonesia timezone (WITA - UTC+8)</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24; margin: 0;'>❌ TIMEZONE CONFIGURATION INCORRECT!</h4>";
    echo "<p style='color: #721c24; margin: 5px 0 0 0;'>Expected: Asia/Makassar, Current: $currentTimezone</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>✅ All PHP files now have timezone setting</li>";
echo "<li>✅ Database connection sets MySQL timezone</li>";
echo "<li>✅ All date/time functions use Makassar time</li>";
echo "<li>✅ Invoice numbers use local date</li>";
echo "<li>✅ Reports show local time</li>";
echo "</ol>";

echo "<p><strong>Test completed at:</strong> " . date('d F Y, H:i:s') . " WITA</p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background-color: #f8f9fa;
}
h2, h3 { 
    color: #333; 
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}
table {
    width: 100%;
    margin: 10px 0;
    background: white;
}
th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
}
td {
    padding: 10px;
}
tr:nth-child(even) {
    background-color: #f8f9fa;
}
.success { color: green; }
.error { color: red; }
</style>
