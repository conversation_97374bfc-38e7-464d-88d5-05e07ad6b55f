<?php
/**
 * Simple MySQLi Connection Test
 */

echo "<h2>MySQLi Connection Test</h2>";

// Check if MySQLi extension is loaded
if (!extension_loaded('mysqli')) {
    echo "<h3>❌ MySQLi Extension Not Loaded</h3>";
    echo "<p>Please enable MySQLi extension in php.ini:</p>";
    echo "<ol>";
    echo "<li>Open php.ini file</li>";
    echo "<li>Find and uncomment: <code>extension=mysqli</code></li>";
    echo "<li>Restart AppServ</li>";
    echo "</ol>";
    exit;
}

echo "<h3>✅ MySQLi Extension is Loaded</h3>";

// Database credentials
$host = 'localhost';
$username = 'root';
$password = '8@n90N3!';
$database = 'adi';

try {
    // Test connection without database
    echo "<h3>1. Testing Basic Connection...</h3>";
    $mysqli = new mysqli($host, $username, $password);

    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }

    echo "✅ Basic connection successful!<br>";
    echo "MySQL Version: " . $mysqli->server_info . "<br>";

    // Check if database exists
    echo "<h3>2. Checking Database '$database'...</h3>";
    $result = $mysqli->query("SHOW DATABASES LIKE '$database'");

    if ($result->num_rows == 0) {
        echo "❌ Database '$database' does not exist. Creating...<br>";
        if ($mysqli->query("CREATE DATABASE $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")) {
            echo "✅ Database '$database' created successfully!<br>";
        } else {
            throw new Exception("Failed to create database: " . $mysqli->error);
        }
    } else {
        echo "✅ Database '$database' exists!<br>";
    }

    $mysqli->close();

    // Connect to specific database
    echo "<h3>3. Connecting to Database '$database'...</h3>";
    $mysqli = new mysqli($host, $username, $password, $database);

    if ($mysqli->connect_error) {
        throw new Exception("Connection to database failed: " . $mysqli->connect_error);
    }

    $mysqli->set_charset("utf8mb4");
    echo "✅ Connected to database '$database' successfully!<br>";

    // Test a simple query
    echo "<h3>4. Testing Simple Query...</h3>";
    $result = $mysqli->query("SELECT 'Hello MySQLi!' as message, NOW() as current_datetime");

    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ Query successful!<br>";
        echo "Message: " . $row['message'] . "<br>";
        echo "Current Time: " . $row['current_datetime'] . "<br>";
    } else {
        throw new Exception("Query failed: " . $mysqli->error);
    }

    // Test prepared statement
    echo "<h3>5. Testing Prepared Statement...</h3>";
    $stmt = $mysqli->prepare("SELECT ? as test_param, ? as test_number");

    if ($stmt) {
        $param1 = "Test String";
        $param2 = 123;
        $stmt->bind_param("si", $param1, $param2);
        $stmt->execute();

        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        echo "✅ Prepared statement successful!<br>";
        echo "Test Param: " . $row['test_param'] . "<br>";
        echo "Test Number: " . $row['test_number'] . "<br>";

        $stmt->close();
    } else {
        throw new Exception("Prepared statement failed: " . $mysqli->error);
    }

    $mysqli->close();

    echo "<h3>✅ All MySQLi Tests Passed!</h3>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>Run <a href='test_database.php'>test_database.php</a> to test the application database class</li>";
    echo "<li>Import <code>database_schema.sql</code> to create tables</li>";
    echo "<li>Access the application at <a href='index.php'>index.php</a></li>";
    echo "</ul>";

} catch (mysqli_sql_exception $e) {
    echo "<h3>❌ MySQLi Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>Common Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check if MySQL/MariaDB service is running</li>";
    echo "<li>Verify database credentials</li>";
    echo "<li>Check if port 3306 is available</li>";
    echo "<li>Ensure MySQL user has proper permissions</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<h3>❌ Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h2, h3 {
    color: #333;
}
code {
    background-color: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
ul, ol {
    margin-left: 20px;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
