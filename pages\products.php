<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Data Produk';
$currentPage = 'products';
$pageIcon = 'fas fa-box';
$breadcrumb = [
    ['title' => 'Master Data', 'url' => '#'],
    ['title' => 'Data Produk']
];

// Get categories for dropdown
$categories = $db->fetchAll("SELECT * FROM categories ORDER BY name");

include '../includes/header.php';
?>

<div class="card shadow">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-white">
            <i class="fas fa-box me-2"></i>
            Data Produk
        </h6>
        <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#productModal">
            <i class="fas fa-plus me-1"></i>
            Tambah Produk
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="productsTable" class="table table-striped table-hover" style="width:100%">
                <thead>
                    <tr>
                        <th>Kode</th>
                        <th>Nama Produk</th>
                        <th>Kategori</th>
                        <th>Harga Beli</th>
                        <th>Harga Jual</th>
                        <th>Stok</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Product Modal -->
<div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalLabel">Tambah Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="productForm">
                <div class="modal-body">
                    <input type="hidden" id="productId" name="id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productCode" class="form-label">Kode Produk *</label>
                                <input type="text" class="form-control" id="productCode" name="code" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categoryId" class="form-label">Kategori *</label>
                                <select class="form-select" id="categoryId" name="category_id" required>
                                    <option value="">Pilih Kategori</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="productName" class="form-label">Nama Produk *</label>
                        <input type="text" class="form-control" id="productName" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="productDescription" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productUnit" class="form-label">Satuan</label>
                                <select class="form-select" id="productUnit" name="unit">
                                    <option value="pcs">Pcs</option>
                                    <option value="set">Set</option>
                                    <option value="unit">Unit</option>
                                    <option value="meter">Meter</option>
                                    <option value="kg">Kg</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="stockQuantity" class="form-label">Stok Awal</label>
                                <input type="number" class="form-control" id="stockQuantity" name="stock_quantity" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="minStock" class="form-label">Stok Minimum</label>
                                <input type="number" class="form-control" id="minStock" name="min_stock" min="0" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="purchasePrice" class="form-label">Harga Beli *</label>
                                <input type="text" class="form-control currency-input" id="purchasePrice" name="purchase_price" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sellingPrice" class="form-label">Harga Jual *</label>
                                <input type="text" class="form-control currency-input" id="sellingPrice" name="selling_price" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                            <label class="form-check-label" for="isActive">
                                Produk Aktif
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $("#productsTable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "../api/products.php",
            type: "POST"
        },
        columns: [
            { data: 0, width: "10%" },
            { data: 1, width: "25%" },
            { data: 2, width: "15%" },
            { data: 3, width: "12%" },
            { data: 4, width: "12%" },
            { data: 5, width: "10%" },
            { data: 6, width: "8%" },
            { data: 7, width: "8%", orderable: false }
        ],
        order: [[0, "asc"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true
    });

    // Add Product
    $("#productForm").on("submit", function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var isEdit = $("#productId").val() !== "";

        // Convert currency inputs to numbers
        var purchasePrice = $("#purchasePrice").val().replace(/[^\d]/g, "");
        var sellingPrice = $("#sellingPrice").val().replace(/[^\d]/g, "");

        formData.set("purchase_price", purchasePrice);
        formData.set("selling_price", sellingPrice);
        formData.set("is_active", $("#isActive").is(":checked") ? 1 : 0);

        $.ajax({
            url: "../api/product_save.php",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $("#productModal").modal("hide");
                    table.ajax.reload();
                    showSuccess(response.message);
                } else {
                    showError(response.message);
                }
            },
            error: function() {
                showError("Terjadi kesalahan saat menyimpan data");
            }
        });
    });

    // Edit Product
    $(document).on("click", ".btn-edit", function() {
        var id = $(this).data("id");

        $.ajax({
            url: "../api/product_get.php",
            type: "GET",
            data: { id: id },
            success: function(response) {
                if (response.success) {
                    var product = response.data;

                    $("#productId").val(product.id);
                    $("#productCode").val(product.code);
                    $("#categoryId").val(product.category_id);
                    $("#productName").val(product.name);
                    $("#productDescription").val(product.description);
                    $("#productUnit").val(product.unit);
                    $("#stockQuantity").val(product.stock_quantity);
                    $("#minStock").val(product.min_stock);
                    $("#purchasePrice").val(formatNumber(product.purchase_price));
                    $("#sellingPrice").val(formatNumber(product.selling_price));
                    $("#isActive").prop("checked", product.is_active == 1);

                    $("#productModalLabel").text("Edit Produk");
                    $("#productModal").modal("show");
                } else {
                    showError(response.message);
                }
            },
            error: function(xhr, status, error) {
                showError("Terjadi kesalahan saat mengambil data produk");
            }
        });
    });

    // View Product Detail
    $(document).on("click", ".btn-view", function() {
        var id = $(this).data("id");

        $.ajax({
            url: "../api/product_get.php",
            type: "GET",
            data: { id: id },
            success: function(response) {
                if (response.success) {
                    var product = response.data;

                    // Create detail modal content
                    var statusBadge = product.is_active == 1
                        ? "<span class=\"badge bg-success\">Aktif</span>"
                        : "<span class=\"badge bg-danger\">Tidak Aktif</span>";

                    var detailContent = "<div class=\"row\">" +
                        "<div class=\"col-md-6\">" +
                            "<table class=\"table table-borderless\">" +
                                "<tr><td><strong>Kode Produk:</strong></td><td>" + product.code + "</td></tr>" +
                                "<tr><td><strong>Nama Produk:</strong></td><td>" + product.name + "</td></tr>" +
                                "<tr><td><strong>Kategori:</strong></td><td>" + (product.category_name || "-") + "</td></tr>" +
                                "<tr><td><strong>Satuan:</strong></td><td>" + product.unit + "</td></tr>" +
                                "<tr><td><strong>Status:</strong></td><td>" + statusBadge + "</td></tr>" +
                            "</table>" +
                        "</div>" +
                        "<div class=\"col-md-6\">" +
                            "<table class=\"table table-borderless\">" +
                                "<tr><td><strong>Harga Beli:</strong></td><td>" + formatRupiah(product.purchase_price) + "</td></tr>" +
                                "<tr><td><strong>Harga Jual:</strong></td><td>" + formatRupiah(product.selling_price) + "</td></tr>" +
                                "<tr><td><strong>Stok:</strong></td><td>" + formatNumber(product.stock_quantity) + " " + product.unit + "</td></tr>" +
                                "<tr><td><strong>Stok Minimum:</strong></td><td>" + formatNumber(product.min_stock) + " " + product.unit + "</td></tr>" +
                                "<tr><td><strong>Laba per Unit:</strong></td><td>" + formatRupiah(product.selling_price - product.purchase_price) + "</td></tr>" +
                            "</table>" +
                        "</div>" +
                    "</div>";

                    if (product.description) {
                        detailContent += "<div class=\"mt-3\"><strong>Deskripsi:</strong><br>" + product.description + "</div>";
                    }

                    // Show in SweetAlert
                    Swal.fire({
                        title: "Detail Produk",
                        html: detailContent,
                        width: "800px",
                        showCloseButton: true,
                        showConfirmButton: false
                    });
                } else {
                    showError(response.message);
                }
            },
            error: function(xhr, status, error) {
                showError("Terjadi kesalahan saat mengambil data produk");
            }
        });
    });

    // Delete Product
    $(document).on("click", ".btn-delete", function() {
        var id = $(this).data("id");

        confirmDelete(function() {
            $.ajax({
                url: "../api/product_delete.php",
                type: "POST",
                data: { id: id },
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload();
                        showSuccess(response.message);
                    } else {
                        showError(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showError("Terjadi kesalahan saat menghapus produk");
                }
            });
        });
    });

    // Reset form when modal is hidden
    $("#productModal").on("hidden.bs.modal", function() {
        $("#productForm")[0].reset();
        $("#productId").val("");
        $("#productModalLabel").text("Tambah Produk");
    });
});
</script>
';

include '../includes/footer.php';
?>
