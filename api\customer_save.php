<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validate required fields
    $required = ['code', 'name'];
    foreach ($required as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Field $field is required");
        }
    }
    
    $id = $_POST['id'] ?? null;
    $code = sanitizeInput($_POST['code']);
    $name = sanitizeInput($_POST['name']);
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $is_active = intval($_POST['is_active'] ?? 1);
    
    // Validate email if provided
    if (!empty($email) && !isValidEmail($email)) {
        throw new Exception('Format email tidak valid');
    }
    
    $db->beginTransaction();
    
    if ($id) {
        // Update existing customer
        
        // Check if code is unique (excluding current customer)
        $existing = $db->fetchOne(
            "SELECT id FROM customers WHERE code = ? AND id != ?", 
            [$code, $id]
        );
        if ($existing) {
            throw new Exception('Kode pelanggan sudah digunakan');
        }
        
        // Check if customer exists
        $customer = $db->fetchOne("SELECT id FROM customers WHERE id = ?", [$id]);
        if (!$customer) {
            throw new Exception('Pelanggan tidak ditemukan');
        }
        
        $db->update('customers', [
            'code' => $code,
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'address' => $address,
            'is_active' => $is_active,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$id]);
        
        $message = 'Pelanggan berhasil diperbarui';
        
    } else {
        // Create new customer
        
        // Check if code is unique
        $existing = $db->fetchOne("SELECT id FROM customers WHERE code = ?", [$code]);
        if ($existing) {
            throw new Exception('Kode pelanggan sudah digunakan');
        }
        
        $id = $db->insert('customers', [
            'code' => $code,
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'address' => $address,
            'is_active' => $is_active
        ]);
        
        $message = 'Pelanggan berhasil ditambahkan';
    }
    
    $db->commit();
    
    jsonResponse(true, $message, ['id' => $id]);
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
