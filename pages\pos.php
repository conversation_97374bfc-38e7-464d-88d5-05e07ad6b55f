<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Form Penjualan';
$currentPage = 'pos';
$pageIcon = 'fas fa-cash-register';
$breadcrumb = [
    ['title' => 'Transaksi', 'url' => '#'],
    ['title' => 'Form Penjualan']
];

// Get customers for dropdown
$customers = $db->fetchAll("SELECT * FROM customers WHERE is_active = 1 ORDER BY name");

// Get categories for filter
$categories = $db->fetchAll("SELECT * FROM categories ORDER BY name");

include '../includes/header.php';
?>

<div class="row">
    <!-- Product Selection -->
    <div class="col-lg-7">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Pilih Produk
                </h6>
            </div>
            <div class="card-body">
                <!-- Product Filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <select class="form-select" id="categoryFilter">
                            <option value="">Semua Kategori</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control" id="productSearch" placeholder="Cari produk...">
                    </div>
                </div>

                <!-- Product Grid -->
                <div id="productGrid" class="row">
                    <!-- Products will be loaded here -->
                </div>

                <!-- Loading -->
                <div id="productLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Memuat produk...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Shopping Cart -->
    <div class="col-lg-5">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-receipt me-2"></i>
                    Keranjang Belanja
                </h6>
            </div>
            <div class="card-body">
                <!-- Customer Selection -->
                <div class="mb-3">
                    <label for="customerId" class="form-label">Pelanggan</label>
                    <div class="input-group">
                        <select class="form-select" id="customerId" name="customer_id">
                            <option value="">Pilih Pelanggan</option>
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['id']; ?>">
                                <?php echo htmlspecialchars($customer['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#quickCustomerModal">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- Cart Items -->
                <div class="mb-3">
                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                        <table class="table table-sm" id="cartTable">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th>Produk</th>
                                    <th>Qty</th>
                                    <th>Harga Asli</th>
                                    <th>Diskon (Rp)</th>
                                    <th>Harga Final</th>
                                    <th>Total</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="cartItems">
                                <tr id="emptyCart">
                                    <td colspan="7" class="text-center text-muted py-3">
                                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                        <p>Keranjang kosong</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="border-top pt-3">
                    <div class="row mb-2">
                        <div class="col-6"><strong>Subtotal:</strong></div>
                        <div class="col-6 text-end" id="cartSubtotal">Rp 0</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6"><strong>Total Item:</strong></div>
                        <div class="col-6 text-end" id="cartItemCount">0</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6"><h5>Total:</h5></div>
                        <div class="col-6 text-end"><h5 id="cartTotal">Rp 0</h5></div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" id="processPayment" disabled>
                            <i class="fas fa-credit-card me-2"></i>
                            Proses Pembayaran
                        </button>
                        <button type="button" class="btn btn-outline-danger" id="clearCart">
                            <i class="fas fa-trash me-2"></i>
                            Kosongkan Keranjang
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Add Customer Modal -->
<div class="modal fade" id="quickCustomerModal" tabindex="-1" aria-labelledby="quickCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickCustomerModalLabel">Tambah Pelanggan Cepat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="quickCustomerForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="quickCustomerName" class="form-label">Nama Pelanggan *</label>
                        <input type="text" class="form-control" id="quickCustomerName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="quickCustomerPhone" class="form-label">Telepon</label>
                        <input type="text" class="form-control" id="quickCustomerPhone" name="phone">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">Proses Pembayaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Detail Transaksi</h6>
                        <div id="paymentSummary"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Pembayaran</h6>

                        <!-- Payment Type Selection -->
                        <div class="mb-3">
                            <label class="form-label">Jenis Pembayaran</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="paymentType" id="paymentTypeFull" value="full" checked>
                                <label class="form-check-label" for="paymentTypeFull">
                                    <strong>Bayar Lunas</strong>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="paymentType" id="paymentTypeDP" value="dp">
                                <label class="form-check-label" for="paymentTypeDP">
                                    <strong>Bayar DP (Uang Muka)</strong>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="paymentAmount" class="form-label">Jumlah Bayar</label>
                            <input type="text" class="form-control currency-input" id="paymentAmount" required>
                            <div class="form-text" id="paymentHint">Masukkan jumlah pembayaran</div>
                        </div>

                        <div class="mb-3" id="changeSection">
                            <label for="paymentChange" class="form-label">Kembalian</label>
                            <input type="text" class="form-control" id="paymentChange" readonly>
                        </div>

                        <div class="mb-3" id="remainingSection" style="display: none;">
                            <label for="paymentRemaining" class="form-label">Sisa Bayar</label>
                            <input type="text" class="form-control" id="paymentRemaining" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="paymentNotes" class="form-label">Catatan</label>
                            <textarea class="form-control" id="paymentNotes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success" id="completeSale">
                    <i class="fas fa-check me-2"></i>
                    Selesaikan Transaksi
                </button>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script src="' . getBaseUrl() . 'assets/js/pos.js"></script>
';

include '../includes/footer.php';
?>
