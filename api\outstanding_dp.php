<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $db = new Database();

    $action = $_GET['action'] ?? 'list';

    // Debug: Log the action
    error_log("Outstanding DP API - Action: " . $action);

    switch ($action) {
        case 'list':
            getOutstandingList($db);
            break;

        case 'summary':
            getOutstandingSummary($db);
            break;

        case 'detail':
            getTransactionDetail($db);
            break;

        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    error_log("Outstanding DP API Error: " . $e->getMessage());
    jsonResponse(false, $e->getMessage());
}

function getOutstandingList($db) {
    $search = $_GET['search'] ?? '';
    $status = $_GET['status'] ?? '';
    $sortBy = $_GET['sort'] ?? 'sale_date DESC';

    // Debug: Log parameters
    error_log("Outstanding List - Search: '$search', Status: '$status', Sort: '$sortBy'");

    // Build WHERE clause
    $whereConditions = ["s.payment_status = 'partial'"];
    $params = [];

    if (!empty($search)) {
        $whereConditions[] = "(s.customer_name LIKE ? OR s.invoice_number LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    $whereClause = implode(' AND ', $whereConditions);

    // Validate sort by
    $allowedSorts = [
        'sale_date DESC', 'sale_date ASC',
        'remaining_amount DESC', 'remaining_amount ASC',
        'customer_name ASC', 'customer_name DESC'
    ];

    if (!in_array($sortBy, $allowedSorts)) {
        $sortBy = 'sale_date DESC';
    }

    $query = "
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE $whereClause
        ORDER BY $sortBy
    ";

    // Debug: Log query and params
    error_log("Outstanding Query: " . $query);
    error_log("Outstanding Params: " . json_encode($params));

    $results = $db->fetchAll($query, $params);

    // Debug: Log results count
    error_log("Outstanding Results Count: " . count($results));

    // Format data
    $formattedResults = array_map(function($row) {
        return [
            'id' => $row['id'],
            'invoice_number' => $row['invoice_number'],
            'customer_name' => $row['customer_name'],
            'sale_date' => formatDate($row['sale_date']),
            'sale_date_raw' => $row['sale_date'],
            'total_amount' => floatval($row['total_amount']),
            'paid_amount' => floatval($row['paid_amount']),
            'remaining_amount' => floatval($row['remaining_amount']),
            'payment_progress' => floatval($row['payment_progress']),
            'created_at' => formatDateTime($row['created_at'])
        ];
    }, $results);

    jsonResponse(true, 'Data retrieved successfully', $formattedResults);
}

function getOutstandingSummary($db) {
    $query = "
        SELECT
            COUNT(*) as total_customers,
            SUM(total_amount) as total_amount,
            SUM(paid_amount) as total_paid,
            SUM(remaining_amount) as total_outstanding,
            AVG(remaining_amount) as average_remaining
        FROM sales
        WHERE payment_status = 'partial'
    ";

    $result = $db->fetchOne($query);

    $summary = [
        'total_customers' => intval($result['total_customers']),
        'total_amount' => floatval($result['total_amount'] ?? 0),
        'total_paid' => floatval($result['total_paid'] ?? 0),
        'total_outstanding' => floatval($result['total_outstanding'] ?? 0),
        'average_remaining' => floatval($result['average_remaining'] ?? 0)
    ];

    jsonResponse(true, 'Summary retrieved successfully', $summary);
}

function getTransactionDetail($db) {
    $saleId = intval($_GET['id'] ?? 0);

    if (!$saleId) {
        throw new Exception('Sale ID is required');
    }

    // Get sale details
    $sale = $db->fetchOne("
        SELECT
            s.*,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.id = ?
    ", [$saleId]);

    if (!$sale) {
        throw new Exception('Transaction not found');
    }

    // Get sale items
    $items = $db->fetchAll("
        SELECT
            si.*
        FROM sale_items si
        WHERE si.sale_id = ?
        ORDER BY si.id
    ", [$saleId]);

    // Get payment history
    $payments = $db->fetchAll("
        SELECT
            sp.*
        FROM sale_payments sp
        WHERE sp.sale_id = ?
        ORDER BY sp.payment_date DESC, sp.created_at DESC
    ", [$saleId]);

    // Format data
    $formattedSale = [
        'id' => $sale['id'],
        'invoice_number' => $sale['invoice_number'],
        'customer_name' => $sale['customer_name'],
        'sale_date' => formatDate($sale['sale_date']),
        'total_amount' => floatval($sale['total_amount']),
        'paid_amount' => floatval($sale['paid_amount']),
        'remaining_amount' => floatval($sale['remaining_amount']),
        'payment_progress' => floatval($sale['payment_progress']),
        'notes' => $sale['notes'],
        'created_at' => formatDateTime($sale['created_at'])
    ];

    $formattedItems = array_map(function($item) {
        return [
            'product_name' => $item['product_name'],
            'product_code' => $item['product_code'],
            'quantity' => intval($item['quantity']),
            'unit_price' => floatval($item['unit_price']),
            'discount_amount' => floatval($item['discount_amount'] ?? 0),
            'final_price' => floatval($item['final_price'] ?? $item['unit_price']),
            'total_price' => floatval($item['total_price'])
        ];
    }, $items);

    $formattedPayments = array_map(function($payment) {
        return [
            'id' => $payment['id'],
            'payment_date' => formatDate($payment['payment_date']),
            'amount' => floatval($payment['amount']),
            'payment_type' => $payment['payment_type'],
            'notes' => $payment['notes'],
            'created_at' => formatDateTime($payment['created_at'])
        ];
    }, $payments);

    $result = [
        'sale' => $formattedSale,
        'items' => $formattedItems,
        'payments' => $formattedPayments
    ];

    jsonResponse(true, 'Detail retrieved successfully', $result);
}
?>
