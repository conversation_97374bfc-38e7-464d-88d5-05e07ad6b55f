<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $db = new Database();

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON data');
    }

    $saleId = intval($input['sale_id'] ?? 0);
    $paymentAmount = floatval($input['payment_amount'] ?? 0);
    $notes = $input['notes'] ?? '';

    if (!$saleId) {
        throw new Exception('Sale ID is required');
    }

    if ($paymentAmount <= 0) {
        throw new Exception('Payment amount must be greater than 0');
    }

    // Debug logging
    error_log("Process Installment - Sale ID: $saleId, Amount: $paymentAmount");

    $db->beginTransaction();

    // Get current sale data
    $sale = $db->fetchOne("
        SELECT
            id, invoice_number, customer_name, total_amount,
            paid_amount, remaining_amount, payment_status
        FROM sales
        WHERE id = ? AND payment_status = 'partial'
    ", [$saleId]);

    if (!$sale) {
        throw new Exception('Transaction not found or already paid');
    }

    // Validate payment amount
    if ($paymentAmount > $sale['remaining_amount']) {
        throw new Exception('Payment amount exceeds remaining balance');
    }

    // Calculate new amounts
    $newPaidAmount = $sale['paid_amount'] + $paymentAmount;
    $newRemainingAmount = $sale['remaining_amount'] - $paymentAmount;

    // Determine new payment status
    $newPaymentStatus = $newRemainingAmount <= 0 ? 'paid' : 'partial';

    // Update sales record
    error_log("Updating sale ID $saleId: paid=$newPaidAmount, remaining=$newRemainingAmount, status=$newPaymentStatus");

    $updateResult = $db->update('sales', [
        'paid_amount' => $newPaidAmount,
        'remaining_amount' => $newRemainingAmount,
        'payment_status' => $newPaymentStatus
    ], 'id = ?', [$saleId]);

    error_log("Update result: $updateResult rows affected");

    // Insert payment record
    $paymentId = $db->insert('sale_payments', [
        'sale_id' => $saleId,
        'payment_date' => date('Y-m-d'),
        'amount' => $paymentAmount,
        'payment_type' => $newPaymentStatus === 'paid' ? 'full' : 'installment',
        'notes' => $notes
    ]);

    $db->commit();

    $responseMessage = $newPaymentStatus === 'paid'
        ? 'Pembayaran berhasil! Transaksi telah lunas.'
        : 'Pembayaran cicilan berhasil diproses.';

    jsonResponse(true, $responseMessage, [
        'sale_id' => $saleId,
        'payment_id' => $paymentId,
        'invoice_number' => $sale['invoice_number'],
        'customer_name' => $sale['customer_name'],
        'payment_amount' => $paymentAmount,
        'new_paid_amount' => $newPaidAmount,
        'new_remaining_amount' => $newRemainingAmount,
        'payment_status' => $newPaymentStatus,
        'is_fully_paid' => $newPaymentStatus === 'paid'
    ]);

} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
