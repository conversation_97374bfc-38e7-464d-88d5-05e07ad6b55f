<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_POST['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID kategori diperlukan');
    }
    
    // Check if category exists
    $category = $db->fetchOne("SELECT id, name FROM categories WHERE id = ?", [$id]);
    if (!$category) {
        throw new Exception('Kategori tidak ditemukan');
    }
    
    // Check if category is used in products
    $usedInProducts = $db->fetchOne(
        "SELECT COUNT(*) as total FROM products WHERE category_id = ?", 
        [$id]
    )['total'];
    
    if ($usedInProducts > 0) {
        throw new Exception('Kategori tidak dapat dihapus karena masih digunakan oleh ' . $usedInProducts . ' produk');
    }
    
    $db->beginTransaction();
    
    // Delete the category
    $db->delete('categories', 'id = ?', [$id]);
    
    $db->commit();
    
    jsonResponse(true, 'Kategori "' . $category['name'] . '" berhasil dihapus');
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
