<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID pelanggan diperlukan');
    }
    
    // Get customer data
    $customer = $db->fetchOne("
        SELECT 
            *,
            (SELECT COUNT(*) FROM sales WHERE customer_id = customers.id) as total_transactions,
            (SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE customer_id = customers.id) as total_purchases,
            (SELECT COALESCE(SUM(profit), 0) FROM sales WHERE customer_id = customers.id) as total_profit
        FROM customers 
        WHERE id = ?
    ", [$id]);
    
    if (!$customer) {
        throw new Exception('Pelanggan tidak ditemukan');
    }
    
    // Get recent transactions
    $recentSales = $db->fetchAll("
        SELECT 
            invoice_number,
            sale_date,
            total_amount,
            profit,
            created_at
        FROM sales 
        WHERE customer_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ", [$id]);
    
    // Build HTML content
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6>Informasi Pelanggan</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Kode:</strong></td>
                    <td>' . htmlspecialchars($customer['code']) . '</td>
                </tr>
                <tr>
                    <td><strong>Nama:</strong></td>
                    <td>' . htmlspecialchars($customer['name']) . '</td>
                </tr>
                <tr>
                    <td><strong>Telepon:</strong></td>
                    <td>' . htmlspecialchars($customer['phone'] ?: '-') . '</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>' . htmlspecialchars($customer['email'] ?: '-') . '</td>
                </tr>
                <tr>
                    <td><strong>Alamat:</strong></td>
                    <td>' . htmlspecialchars($customer['address'] ?: '-') . '</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>' . ($customer['is_active'] ? '<span class="badge bg-success">Aktif</span>' : '<span class="badge bg-danger">Tidak Aktif</span>') . '</td>
                </tr>
                <tr>
                    <td><strong>Bergabung:</strong></td>
                    <td>' . formatDate($customer['created_at']) . '</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6>Statistik Pembelian</h6>
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>' . $customer['total_transactions'] . '</h4>
                            <small>Total Transaksi</small>
                        </div>
                    </div>
                </div>
                <div class="col-12 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>' . formatRupiah($customer['total_purchases']) . '</h4>
                            <small>Total Pembelian</small>
                        </div>
                    </div>
                </div>
                <div class="col-12 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>' . formatRupiah($customer['total_profit']) . '</h4>
                            <small>Total Profit</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>';
    
    if (!empty($recentSales)) {
        $html .= '
        <hr>
        <h6>Transaksi Terbaru</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>Invoice</th>
                        <th>Tanggal</th>
                        <th>Total</th>
                        <th>Profit</th>
                    </tr>
                </thead>
                <tbody>';
        
        foreach ($recentSales as $sale) {
            $html .= '
                    <tr>
                        <td>' . htmlspecialchars($sale['invoice_number']) . '</td>
                        <td>' . formatDate($sale['sale_date']) . '</td>
                        <td>' . formatRupiah($sale['total_amount']) . '</td>
                        <td>' . formatRupiah($sale['profit']) . '</td>
                    </tr>';
        }
        
        $html .= '
                </tbody>
            </table>
        </div>';
    } else {
        $html .= '
        <hr>
        <div class="text-center text-muted py-3">
            <i class="fas fa-receipt fa-2x mb-2"></i>
            <p>Belum ada transaksi</p>
        </div>';
    }
    
    jsonResponse(true, 'Detail pelanggan berhasil diambil', ['html' => $html]);
    
} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
