<?php
// Daily Sales Data
$dailyData = $db->fetchAll("
    SELECT 
        sale_date,
        COUNT(*) as total_transactions,
        SUM(total_amount) as total_sales,
        SUM(total_cost) as total_cost,
        SUM(profit) as total_profit
    FROM sales
    WHERE sale_date BETWEEN ? AND ?
    GROUP BY sale_date
    ORDER BY sale_date
", [$dateFrom, $dateTo]);
?>

<!-- Daily Sales -->
<div class="summary-section">
    <div class="summary-title">PENJUALAN HARIAN</div>
    <table class="data-table">
        <thead>
            <tr>
                <th>Tanggal</th>
                <th>Transaksi</th>
                <th>Total Penjualan</th>
                <th>Total HPP</th>
                <th>Laba</th>
                <th>Margin (%)</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($dailyData)): ?>
                <?php 
                $totalTransactions = 0;
                $totalSales = 0;
                $totalCost = 0;
                $totalProfit = 0;
                ?>
                <?php foreach ($dailyData as $day): ?>
                <?php 
                $margin = $day['total_cost'] > 0 ? round(($day['total_profit'] / $day['total_cost']) * 100, 1) : 0;
                $totalTransactions += $day['total_transactions'];
                $totalSales += $day['total_sales'];
                $totalCost += $day['total_cost'];
                $totalProfit += $day['total_profit'];
                ?>
                <tr>
                    <td class="center"><?php echo formatDate($day['sale_date']); ?></td>
                    <td class="number"><?php echo number_format($day['total_transactions']); ?></td>
                    <td class="number"><?php echo formatRupiah($day['total_sales']); ?></td>
                    <td class="number"><?php echo formatRupiah($day['total_cost']); ?></td>
                    <td class="number"><?php echo formatRupiah($day['total_profit']); ?></td>
                    <td class="number"><?php echo $margin; ?>%</td>
                </tr>
                <?php endforeach; ?>
                
                <!-- Total Row -->
                <?php $totalMargin = $totalCost > 0 ? round(($totalProfit / $totalCost) * 100, 1) : 0; ?>
                <tr style="background-color: #f0f0f0; font-weight: bold;">
                    <td class="center">TOTAL</td>
                    <td class="number"><?php echo number_format($totalTransactions); ?></td>
                    <td class="number"><?php echo formatRupiah($totalSales); ?></td>
                    <td class="number"><?php echo formatRupiah($totalCost); ?></td>
                    <td class="number"><?php echo formatRupiah($totalProfit); ?></td>
                    <td class="number"><?php echo $totalMargin; ?>%</td>
                </tr>
            <?php else: ?>
                <tr>
                    <td colspan="6" class="center">Tidak ada data penjualan</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
