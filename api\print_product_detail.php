<?php
// Product Sales Data
$productData = $db->fetchAll("
    SELECT 
        si.product_code,
        si.product_name,
        SUM(si.quantity) as total_qty,
        SUM(si.total_price) as total_sales,
        SUM(si.total_cost) as total_cost,
        SUM(si.profit) as total_profit,
        AVG(si.unit_price) as avg_price
    FROM sale_items si
    JOIN sales s ON si.sale_id = s.id
    WHERE s.sale_date BETWEEN ? AND ?
    GROUP BY si.product_id, si.product_code, si.product_name
    ORDER BY total_sales DESC
", [$dateFrom, $dateTo]);
?>

<!-- Product Sales -->
<div class="summary-section">
    <div class="summary-title">PENJUALAN PER PRODUK</div>
    <table class="data-table">
        <thead>
            <tr>
                <th>No</th>
                <th>Kode</th>
                <th>Nama Produk</th>
                <th>Qty</th>
                <th>Rata-rata <PERSON></th>
                <th>Total Penjualan</th>
                <th>Total HPP</th>
                <th>Laba</th>
                <th>Margin (%)</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($productData)): ?>
                <?php 
                $no = 1;
                $totalQty = 0;
                $totalSales = 0;
                $totalCost = 0;
                $totalProfit = 0;
                ?>
                <?php foreach ($productData as $product): ?>
                <?php 
                $margin = $product['total_cost'] > 0 ? round(($product['total_profit'] / $product['total_cost']) * 100, 1) : 0;
                $totalQty += $product['total_qty'];
                $totalSales += $product['total_sales'];
                $totalCost += $product['total_cost'];
                $totalProfit += $product['total_profit'];
                ?>
                <tr>
                    <td class="center"><?php echo $no++; ?></td>
                    <td class="center"><?php echo htmlspecialchars($product['product_code']); ?></td>
                    <td><?php echo htmlspecialchars($product['product_name']); ?></td>
                    <td class="number"><?php echo number_format($product['total_qty']); ?></td>
                    <td class="number"><?php echo formatRupiah($product['avg_price']); ?></td>
                    <td class="number"><?php echo formatRupiah($product['total_sales']); ?></td>
                    <td class="number"><?php echo formatRupiah($product['total_cost']); ?></td>
                    <td class="number"><?php echo formatRupiah($product['total_profit']); ?></td>
                    <td class="number"><?php echo $margin; ?>%</td>
                </tr>
                <?php endforeach; ?>
                
                <!-- Total Row -->
                <?php 
                $totalMargin = $totalCost > 0 ? round(($totalProfit / $totalCost) * 100, 1) : 0;
                $avgPrice = $totalQty > 0 ? $totalSales / $totalQty : 0;
                ?>
                <tr style="background-color: #f0f0f0; font-weight: bold;">
                    <td colspan="3" class="center">TOTAL</td>
                    <td class="number"><?php echo number_format($totalQty); ?></td>
                    <td class="number"><?php echo formatRupiah($avgPrice); ?></td>
                    <td class="number"><?php echo formatRupiah($totalSales); ?></td>
                    <td class="number"><?php echo formatRupiah($totalCost); ?></td>
                    <td class="number"><?php echo formatRupiah($totalProfit); ?></td>
                    <td class="number"><?php echo $totalMargin; ?>%</td>
                </tr>
            <?php else: ?>
                <tr>
                    <td colspan="9" class="center">Tidak ada data produk</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
