/* Custom CSS for POS Application */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

/* Sidebar Navigation */
.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 2px 0;
    border-radius: 8px;
    transition: all 0.3s;
    text-decoration: none;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.sidebar .sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    font-weight: 600;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

/* Card Styles */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0,0,0,0.15) !important;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* DataTables Custom Styles */
.dataTables_wrapper .dataTables_length select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_info {
    padding-top: 0.85rem;
}

.dataTables_wrapper .dataTables_paginate {
    padding-top: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem;
    margin-left: 2px;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    background: white;
    color: #5a5c69;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #eaecf4;
    border-color: #d1d3e2;
    color: #5a5c69;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

/* Table Styles */
.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Button Styles */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
}

/* Modal Styles */
.modal-header {
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* Form Styles */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Currency Input */
.currency-input {
    text-align: right;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Mobile Navigation */
.navbar-dark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .main-content {
        margin-left: 0 !important;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .sidebar .nav-link {
        padding: 10px 15px;
        margin: 1px 0;
    }

    .sidebar .sidebar-heading {
        padding: 0 15px;
        margin-top: 0.5rem;
        margin-bottom: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .btn,
    .modal,
    .alert,
    .card-header,
    .no-print {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 12px;
    }

    .page-break {
        page-break-before: always;
    }

    /* Report specific print styles */
    .report-container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .data-table {
        font-size: 10px !important;
    }

    .data-table th,
    .data-table td {
        padding: 4px !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
