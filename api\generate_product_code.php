<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }
    
    $categoryId = $_GET['category_id'] ?? null;
    
    if (!$categoryId) {
        throw new Exception('Category ID diperlukan');
    }
    
    // Get category info
    $category = $db->fetchOne("SELECT name FROM categories WHERE id = ?", [$categoryId]);
    
    if (!$category) {
        throw new Exception('Kategori tidak ditemukan');
    }
    
    // Generate prefix based on category name
    $prefixes = [
        'Kusen Pintu' => 'KP',
        'Kusen Jendela' => 'KJ',
        'Daun Pintu' => 'DP',
        '<PERSON>un <PERSON>' => 'DJ',
        'Ventilasi' => 'VT',
        'Aksesoris' => 'AK'
    ];
    
    $categoryName = $category['name'];
    $prefix = $prefixes[$categoryName] ?? 'PR';
    
    // Get last product code with this prefix
    $lastProduct = $db->fetchOne(
        "SELECT code FROM products WHERE code LIKE ? ORDER BY code DESC LIMIT 1",
        [$prefix . '%']
    );
    
    $nextNumber = 1;
    if ($lastProduct) {
        $lastNumber = intval(substr($lastProduct['code'], strlen($prefix)));
        $nextNumber = $lastNumber + 1;
    }
    
    $newCode = $prefix . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    
    // Make sure code is unique
    while ($db->fetchOne("SELECT id FROM products WHERE code = ?", [$newCode])) {
        $nextNumber++;
        $newCode = $prefix . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }
    
    jsonResponse(true, 'Kode produk berhasil di-generate', ['code' => $newCode]);
    
} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
