<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }

    $id = $_GET['id'] ?? null;

    if (!$id) {
        throw new Exception('ID transaksi diperlukan');
    }

    // Get sale header
    $sale = $db->fetchOne("
        SELECT
            s.*,
            c.name as customer_db_name,
            c.phone as customer_phone,
            c.address as customer_address
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.id = ?
    ", [$id]);

    if (!$sale) {
        throw new Exception('Transaksi tidak ditemukan');
    }

    // Get sale items
    $items = $db->fetchAll("
        SELECT
            si.*,
            p.unit
        FROM sale_items si
        LEFT JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ", [$id]);

    // Get payment history if DP transaction
    $payments = [];
    if (($sale['payment_status'] ?? 'paid') === 'partial') {
        $payments = $db->fetchAll("
            SELECT
                sp.*
            FROM sale_payments sp
            WHERE sp.sale_id = ?
            ORDER BY sp.payment_date DESC, sp.created_at DESC
        ", [$id]);
    }

    $customerName = $sale['customer_db_name'] ?: $sale['customer_name'];
    $profitPercentage = $sale['total_cost'] > 0
        ? round(($sale['profit'] / $sale['total_cost']) * 100, 1)
        : 0;

    // Build HTML content
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6>Informasi Transaksi</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Invoice:</strong></td>
                    <td>' . htmlspecialchars($sale['invoice_number']) . '</td>
                </tr>
                <tr>
                    <td><strong>Tanggal:</strong></td>
                    <td>' . formatDate($sale['sale_date']) . '</td>
                </tr>
                <tr>
                    <td><strong>Waktu:</strong></td>
                    <td>' . formatDateTime($sale['created_at']) . '</td>
                </tr>
                <tr>
                    <td><strong>Kasir:</strong></td>
                    <td>' . htmlspecialchars($sale['created_by']) . '</td>
                </tr>
                <tr>
                    <td><strong>Catatan:</strong></td>
                    <td>' . htmlspecialchars($sale['notes'] ?: '-') . '</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6>Informasi Pelanggan</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Nama:</strong></td>
                    <td>' . htmlspecialchars($customerName) . '</td>
                </tr>
                <tr>
                    <td><strong>Telepon:</strong></td>
                    <td>' . htmlspecialchars($sale['customer_phone'] ?: '-') . '</td>
                </tr>
                <tr>
                    <td><strong>Alamat:</strong></td>
                    <td>' . htmlspecialchars($sale['customer_address'] ?: '-') . '</td>
                </tr>
            </table>
        </div>
    </div>';

    // Add payment information if DP transaction
    $paymentStatus = $sale['payment_status'] ?? 'paid';
    if ($paymentStatus === 'partial') {
        $html .= '
        <hr>
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-money-bill-wave me-2"></i>Informasi Pembayaran</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td><span class="badge bg-warning text-dark">DP (Belum Lunas)</span></td>
                    </tr>
                    <tr>
                        <td><strong>Total Transaksi:</strong></td>
                        <td>' . formatRupiah($sale['total_amount']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Sudah Dibayar:</strong></td>
                        <td class="text-success">' . formatRupiah($sale['paid_amount'] ?? 0) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Sisa Bayar:</strong></td>
                        <td class="text-danger"><strong>' . formatRupiah($sale['remaining_amount'] ?? 0) . '</strong></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-history me-2"></i>History Pembayaran</h6>';

        if (!empty($payments)) {
            $html .= '
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Tanggal</th>
                                <th>Jenis</th>
                                <th class="text-end">Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>';

            foreach ($payments as $payment) {
                $badgeClass = $payment['payment_type'] === 'dp' ? 'bg-warning' :
                             ($payment['payment_type'] === 'full' ? 'bg-success' : 'bg-info');

                $html .= '
                            <tr>
                                <td>' . formatDate($payment['payment_date']) . '</td>
                                <td><span class="badge ' . $badgeClass . '">' . strtoupper($payment['payment_type']) . '</span></td>
                                <td class="text-end">' . formatRupiah($payment['amount']) . '</td>
                            </tr>';
            }

            $html .= '
                        </tbody>
                    </table>
                </div>';
        } else {
            $html .= '<p class="text-muted">Belum ada history pembayaran</p>';
        }

        $html .= '
            </div>
        </div>';
    } else {
        $html .= '
        <div class="alert alert-success mt-3">
            <i class="fas fa-check-circle me-2"></i>
            <strong>Status Pembayaran: LUNAS</strong>
        </div>';
    }

    $html .= '

    <hr>

    <h6>Detail Produk</h6>
    <div class="table-responsive">
        <table class="table table-sm table-striped">
            <thead>
                <tr>
                    <th>Kode</th>
                    <th>Produk</th>
                    <th>Qty</th>
                    <th>Harga Asli</th>
                    <th>Diskon</th>
                    <th>Harga Final</th>
                    <th>Total</th>
                    <th>HPP</th>
                    <th>Laba</th>
                </tr>
            </thead>
            <tbody>';

    $totalDiscount = 0;
    foreach ($items as $item) {
        $discountAmount = floatval($item['discount_amount'] ?? 0);
        $finalPrice = floatval($item['final_price'] ?? $item['unit_price']);
        $totalDiscount += $discountAmount;

        $itemProfitPercentage = $item['unit_cost'] > 0
            ? round((($finalPrice - $item['unit_cost']) / $item['unit_cost']) * 100, 1)
            : 0;

        $html .= '
                <tr>
                    <td>' . htmlspecialchars($item['product_code']) . '</td>
                    <td>' . htmlspecialchars($item['product_name']) . '</td>
                    <td>' . $item['quantity'] . ' ' . ($item['unit'] ?: 'pcs') . '</td>
                    <td>' . formatRupiah($item['unit_price']) . '</td>
                    <td>' . ($discountAmount > 0 ? formatRupiah($discountAmount) : '-') . '</td>
                    <td>' . formatRupiah($finalPrice) . '</td>
                    <td>' . formatRupiah($item['total_price']) . '</td>
                    <td>' . formatRupiah($item['total_cost']) . '</td>
                    <td>
                        ' . formatRupiah($item['profit']) . '<br>
                        <small class="text-success">+' . $itemProfitPercentage . '%</small>
                    </td>
                </tr>';
    }

    $html .= '
            </tbody>
            <tfoot>
                <tr class="table-light">
                    <th colspan="4">Total</th>
                    <th>' . formatRupiah($totalDiscount) . '</th>
                    <th>-</th>
                    <th>' . formatRupiah($sale['total_amount']) . '</th>
                    <th>' . formatRupiah($sale['total_cost']) . '</th>
                    <th>
                        ' . formatRupiah($sale['profit']) . '<br>
                        <small class="text-success">+' . $profitPercentage . '%</small>
                    </th>
                </tr>
            </tfoot>
        </table>
    </div>

    <hr>

    <div class="row">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>' . formatRupiah($sale['total_amount']) . '</h4>
                    <small>Total Penjualan</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>' . formatRupiah($sale['total_cost']) . '</h4>
                    <small>Total HPP</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>' . formatRupiah($sale['profit']) . '</h4>
                    <small>Laba Bersih (' . $profitPercentage . '%)</small>
                </div>
            </div>
        </div>
    </div>';

    jsonResponse(true, 'Detail transaksi berhasil diambil', ['html' => $html]);

} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
