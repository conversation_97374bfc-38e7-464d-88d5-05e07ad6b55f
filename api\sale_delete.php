<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }

    $id = $_POST['id'] ?? null;

    if (!$id) {
        throw new Exception('ID transaksi diperlukan');
    }

    // Check if sale exists
    $sale = $db->fetchOne("SELECT id, invoice_number, payment_status FROM sales WHERE id = ?", [$id]);
    if (!$sale) {
        throw new Exception('Transaksi tidak ditemukan');
    }

    // Get sale items for stock restoration
    $items = $db->fetchAll("SELECT product_id, quantity FROM sale_items WHERE sale_id = ?", [$id]);

    $db->beginTransaction();

    // Restore product stock
    foreach ($items as $item) {
        $db->query(
            "UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?",
            [$item['quantity'], $item['product_id']]
        );
    }

    // Delete payment records if exists (for DP transactions)
    $db->delete('sale_payments', 'sale_id = ?', [$id]);

    // Delete sale items first (foreign key constraint)
    $db->delete('sale_items', 'sale_id = ?', [$id]);

    // Delete sale header
    $db->delete('sales', 'id = ?', [$id]);

    $db->commit();

    jsonResponse(true, 'Transaksi "' . $sale['invoice_number'] . '" berhasil dihapus dan stok produk telah dikembalikan');

} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
