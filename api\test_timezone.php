<?php
// Test timezone consistency across all modules

echo "<h2>🕐 Timezone Test - Riwayat Penjualan</h2>";

echo "<h3>1. Default PHP Timezone (Before Setting):</h3>";
echo "<p>Default: " . date_default_timezone_get() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s T') . "</p>";

echo "<h3>2. Set Makassar Timezone:</h3>";
date_default_timezone_set('Asia/Makassar');
echo "<p>After Setting: " . date_default_timezone_get() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s T') . "</p>";

echo "<h3>3. Test API Files Timezone:</h3>";

// Test each API file
$apiFiles = [
    'sales.php' => 'Sales List API',
    'sale_detail.php' => 'Sale Detail API',
    'sale_delete.php' => 'Sale Delete API',
    'print_receipt.php' => 'Print Receipt API',
    'outstanding_dp.php' => 'Outstanding DP API',
    'process_installment.php' => 'Process Installment API'
];

echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f0f0f0;'><th>API File</th><th>Description</th><th>Timezone Check</th></tr>";

foreach ($apiFiles as $file => $description) {
    $filePath = __DIR__ . '/' . $file;
    $status = '❌ File not found';

    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);

        if (strpos($content, "date_default_timezone_set('Asia/Makassar')") !== false) {
            $status = '✅ Timezone set correctly';
        } else {
            $status = '⚠️ Timezone not set';
        }
    }

    echo "<tr>";
    echo "<td><strong>$file</strong></td>";
    echo "<td>$description</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>4. Test Database Connection with Timezone:</h3>";

try {
    require_once '../config/database.php';
    require_once '../includes/functions.php';

    // Use global database instance
    global $db;

    // Test current timestamp
    try {
        $result = $db->fetchOne("SELECT NOW() as current_time");
        echo "<p><strong>Database Current Time:</strong> " . $result['current_time'] . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>Database time query failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>PHP Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    }

    // Test sample sale data
    $sampleSale = $db->fetchAll("SELECT id, invoice_number, sale_date, created_at FROM sales ORDER BY id DESC LIMIT 1");

    if ($sampleSale && count($sampleSale) > 0) {
        $sale = $sampleSale[0]; // Get first result from array
        echo "<h4>Sample Sale Data:</h4>";
        echo "<ul>";
        echo "<li><strong>Invoice:</strong> " . $sale['invoice_number'] . "</li>";
        echo "<li><strong>Sale Date:</strong> " . $sale['sale_date'] . " → " . formatDate($sale['sale_date']) . "</li>";
        echo "<li><strong>Created At:</strong> " . $sale['created_at'] . " → " . formatDateTime($sale['created_at']) . "</li>";
        echo "</ul>";
    } else {
        echo "<p>No sales data found</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}

echo "<h3>5. Test formatDate and formatDateTime Functions:</h3>";

$testDate = '2024-12-26';
$testDateTime = '2024-12-26 14:30:45';

echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>Function</th><th>Input</th><th>Output</th></tr>";

echo "<tr>";
echo "<td><strong>formatDate()</strong></td>";
echo "<td>$testDate</td>";
echo "<td>" . formatDate($testDate) . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>formatDateTime()</strong></td>";
echo "<td>$testDateTime</td>";
echo "<td>" . formatDateTime($testDateTime) . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>date('d/m/Y H:i:s')</strong></td>";
echo "<td>Current time</td>";
echo "<td>" . date('d/m/Y H:i:s') . "</td>";
echo "</tr>";

echo "</table>";

echo "<h3>6. Timezone Comparison:</h3>";

$timezones = [
    'UTC' => 'UTC',
    'Asia/Jakarta' => 'Jakarta (WIB - UTC+7)',
    'Asia/Makassar' => 'Makassar (WITA - UTC+8)',
    'Asia/Jayapura' => 'Jayapura (WIT - UTC+9)'
];

echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>Timezone</th><th>Current Time</th><th>Status</th></tr>";

foreach ($timezones as $tz => $description) {
    $originalTz = date_default_timezone_get();
    date_default_timezone_set($tz);
    $time = date('Y-m-d H:i:s T');
    $status = $tz === 'Asia/Makassar' ? '✅ Active' : '';

    echo "<tr" . ($tz === 'Asia/Makassar' ? " style='background: #d4edda;'" : "") . ">";
    echo "<td><strong>$description</strong></td>";
    echo "<td>$time</td>";
    echo "<td>$status</td>";
    echo "</tr>";

    date_default_timezone_set($originalTz);
}

echo "</table>";

echo "<h3>✅ Summary:</h3>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
echo "<h4 style='color: #155724; margin: 0;'>Timezone Configuration Status:</h4>";
echo "<ul style='color: #155724; margin: 10px 0;'>";
echo "<li>✅ Current timezone: " . date_default_timezone_get() . "</li>";
echo "<li>✅ Current time: " . date('d/m/Y H:i:s T') . "</li>";
echo "<li>✅ All API files should have timezone setting</li>";
echo "<li>✅ formatDate() and formatDateTime() functions working</li>";
echo "</ul>";
echo "</div>";

echo "<p><a href='../pages/sales.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Riwayat Penjualan</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f0f0f0; }
p { margin: 10px 0; }
ul { margin: 10px 0; }
</style>
