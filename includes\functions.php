<?php
/**
 * Helper Functions untuk Aplikasi POS
 */

// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

// Format currency ke Rupiah
function formatRupiah($amount) {
    return 'Rp ' . number_format($amount, 0, ',', '.');
}

// Format tanggal Indonesia
function formatDate($date) {
    return date('d/m/Y', strtotime($date));
}

// Format datetime Indonesia
function formatDateTime($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}

// Generate invoice number
function generateInvoiceNumber() {
    return 'INV-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// Generate product code
function generateProductCode($categoryPrefix) {
    return $categoryPrefix . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
}

// Generate customer code
function generateCustomerCode() {
    return 'CUST' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Validate phone number (Indonesia format)
function isValidPhone($phone) {
    return preg_match('/^(\+62|62|0)8[1-9][0-9]{6,9}$/', $phone);
}

// Calculate profit percentage
function calculateProfitPercentage($cost, $selling) {
    if ($cost == 0) return 0;
    return (($selling - $cost) / $cost) * 100;
}

// Get status badge HTML
function getStatusBadge($status) {
    $badges = [
        'active' => '<span class="badge bg-success">Aktif</span>',
        'inactive' => '<span class="badge bg-danger">Tidak Aktif</span>',
        'low_stock' => '<span class="badge bg-warning">Stok Rendah</span>',
        'out_of_stock' => '<span class="badge bg-danger">Stok Habis</span>'
    ];

    return $badges[$status] ?? '<span class="badge bg-secondary">Unknown</span>';
}

// Response JSON untuk AJAX
function jsonResponse($success = true, $message = '', $data = null) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Pagination helper
function getPaginationInfo($total, $limit, $page) {
    $totalPages = ceil($total / $limit);
    $start = ($page - 1) * $limit + 1;
    $end = min($page * $limit, $total);

    return [
        'total' => $total,
        'totalPages' => $totalPages,
        'currentPage' => $page,
        'start' => $start,
        'end' => $end,
        'limit' => $limit
    ];
}

// Check if stock is low
function isLowStock($current, $minimum) {
    return $current <= $minimum;
}

// Get stock status
function getStockStatus($current, $minimum) {
    if ($current == 0) {
        return 'out_of_stock';
    } elseif ($current <= $minimum) {
        return 'low_stock';
    } else {
        return 'normal';
    }
}

// Format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

// Debug helper
function dd($data) {
    echo '<pre>';
    var_dump($data);
    echo '</pre>';
    die();
}

// Log activity
function logActivity($action, $description, $user = 'System') {
    global $db;

    try {
        $db->insert('activity_logs', [
            'user' => $user,
            'action' => $action,
            'description' => $description,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        // Silent fail untuk logging
    }
}

// Get current URL
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

// Get base URL for navigation
function getBaseUrl() {
    $currentFile = basename($_SERVER['PHP_SELF']);
    $currentDir = dirname($_SERVER['PHP_SELF']);

    // If we're in pages folder, go back one level
    if (basename($currentDir) === 'pages') {
        return '../';
    }

    return '';
}

// Generate navigation URL
function getNavUrl($page) {
    $baseUrl = getBaseUrl();

    if ($page === 'dashboard') {
        return $baseUrl . 'index.php';
    }

    return $baseUrl . 'pages/' . $page . '.php';
}

// Redirect helper
function redirect($url) {
    header("Location: $url");
    exit;
}

// Flash message helper
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}
?>
