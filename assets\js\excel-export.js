/**
 * Excel Export using SheetJS
 */

// Test function to check if everything is loaded
function testExport() {
    console.log('Testing export function...');
    console.log('jQuery available:', typeof $ !== 'undefined');
    console.log('XLSX available:', typeof XLSX !== 'undefined');
    console.log('showError available:', typeof showError !== 'undefined');

    if (typeof XLSX !== 'undefined') {
        // Create simple test workbook
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet([['Test', 'Data'], ['Hello', 'World']]);
        XLSX.utils.book_append_sheet(wb, ws, "Test");
        XLSX.writeFile(wb, "test.xlsx");
        console.log('Test file generated successfully');
    }
}

// Format currency for Excel
function formatCurrency(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
}

// Format number for Excel
function formatNumberExcel(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}

// Export to Excel using SheetJS
async function exportToExcel() {
    console.log('Export Excel function called');

    const dateFrom = $("#reportDateFrom").val();
    const dateTo = $("#reportDateTo").val();
    const reportType = $("#reportType").val();

    console.log('Date From:', dateFrom);
    console.log('Date To:', dateTo);
    console.log('Report Type:', reportType);

    if (!dateFrom || !dateTo) {
        if (typeof showError === 'function') {
            showError("Pilih rentang tanggal terlebih dahulu");
        } else {
            alert("Pilih rentang tanggal terlebih dahulu");
        }
        return;
    }

    if (!$("#reportCard").is(":visible")) {
        if (typeof showError === 'function') {
            showError("Generate laporan terlebih dahulu sebelum export");
        } else {
            alert("Generate laporan terlebih dahulu sebelum export");
        }
        return;
    }

    try {
        // Check if XLSX is available
        if (typeof XLSX === 'undefined') {
            throw new Error('SheetJS library not loaded');
        }

        // Show loading
        if (typeof showLoading === 'function') {
            showLoading("Generating Excel file...");
        } else {
            console.log("Generating Excel file...");
        }

        // Fetch data from API
        console.log('Fetching data from API...');
        const response = await fetch(`../api/export_data.php?date_from=${dateFrom}&date_to=${dateTo}&type=${reportType}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('API Response:', result);

        if (!result.success) {
            throw new Error(result.error);
        }

        // Create workbook
        console.log('Creating workbook...');
        const wb = XLSX.utils.book_new();

        // Generate worksheet based on report type
        console.log('Generating worksheet for type:', reportType);
        let ws;
        switch (reportType) {
            case 'summary':
                ws = createSummaryWorksheet(result.data);
                break;
            case 'daily':
                ws = createDailyWorksheet(result.data);
                break;
            case 'product':
                ws = createProductWorksheet(result.data);
                break;
            case 'customer':
                ws = createCustomerWorksheet(result.data);
                break;
            default:
                ws = createSummaryWorksheet(result.data);
        }

        console.log('Worksheet created:', ws);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, "Laporan");

        // Generate filename
        const filename = `Laporan_${reportType}_${dateFrom}_${dateTo}.xlsx`;
        console.log('Saving file:', filename);

        // Save file
        XLSX.writeFile(wb, filename);

        console.log('File saved successfully');

        // Hide loading and show success
        if (typeof hideLoading === 'function') {
            hideLoading();
        }
        if (typeof showSuccess === 'function') {
            showSuccess("File Excel berhasil didownload");
        } else {
            alert("File Excel berhasil didownload");
        }

    } catch (error) {
        console.error('Export error:', error);

        if (typeof hideLoading === 'function') {
            hideLoading();
        }
        if (typeof showError === 'function') {
            showError("Error: " + error.message);
        } else {
            alert("Error: " + error.message);
        }
    }
}

// Create Summary Worksheet
function createSummaryWorksheet(data) {
    const wsData = [];

    // Title and header
    wsData.push(['LAPORAN PENJUALAN']);
    wsData.push(['Periode: ' + data.summary.period]);
    wsData.push(['Dicetak: ' + data.summary.generated]);
    wsData.push([]); // Empty row

    // Summary section
    wsData.push(['RINGKASAN PENJUALAN']);
    wsData.push(['Total Transaksi', formatNumberExcel(data.summary.total_transactions) + ' transaksi']);
    wsData.push(['Total Penjualan', formatCurrency(data.summary.total_sales)]);
    wsData.push(['Total HPP', formatCurrency(data.summary.total_cost)]);
    wsData.push(['Total Laba', formatCurrency(data.summary.total_profit)]);
    wsData.push(['Margin Laba', data.summary.profit_margin + '%']);
    wsData.push([]); // Empty row

    // Top Products
    wsData.push(['PRODUK TERLARIS']);
    wsData.push(['No', 'Nama Produk', 'Qty Terjual', 'Total Penjualan', 'Total Laba']);

    data.details.top_products.forEach((product, index) => {
        wsData.push([
            index + 1,
            product.product_name,
            formatNumberExcel(product.total_qty),
            formatCurrency(product.total_sales),
            formatCurrency(product.total_profit)
        ]);
    });

    wsData.push([]); // Empty row

    // Top Customers
    wsData.push(['PELANGGAN TERBAIK']);
    wsData.push(['No', 'Nama Pelanggan', 'Total Transaksi', 'Total Pembelian']);

    data.details.top_customers.forEach((customer, index) => {
        wsData.push([
            index + 1,
            customer.customer_name,
            formatNumberExcel(customer.total_transactions),
            formatCurrency(customer.total_purchases)
        ]);
    });

    return XLSX.utils.aoa_to_sheet(wsData);
}

// Create Daily Worksheet
function createDailyWorksheet(data) {
    const wsData = [];

    // Title and header
    wsData.push(['LAPORAN PENJUALAN HARIAN']);
    wsData.push(['Periode: ' + data.summary.period]);
    wsData.push(['Dicetak: ' + data.summary.generated]);
    wsData.push([]); // Empty row

    // Summary section
    wsData.push(['RINGKASAN PENJUALAN']);
    wsData.push(['Total Transaksi', formatNumberExcel(data.summary.total_transactions) + ' transaksi']);
    wsData.push(['Total Penjualan', formatCurrency(data.summary.total_sales)]);
    wsData.push(['Total Laba', formatCurrency(data.summary.total_profit)]);
    wsData.push(['Margin Laba', data.summary.profit_margin + '%']);
    wsData.push([]); // Empty row

    // Daily data
    wsData.push(['PENJUALAN HARIAN']);
    wsData.push(['Tanggal', 'Total Transaksi', 'Total Penjualan', 'Total Laba', 'Margin (%)']);

    let totalTransactions = 0;
    let totalSales = 0;
    let totalProfit = 0;

    data.details.forEach(day => {
        const margin = day.total_sales > 0 ? ((day.total_profit / day.total_sales) * 100).toFixed(1) : 0;

        totalTransactions += parseInt(day.total_transactions);
        totalSales += parseFloat(day.total_sales);
        totalProfit += parseFloat(day.total_profit);

        wsData.push([
            day.sale_date,
            formatNumberExcel(day.total_transactions),
            formatCurrency(day.total_sales),
            formatCurrency(day.total_profit),
            margin + '%'
        ]);
    });

    // Total row
    const totalMargin = totalSales > 0 ? ((totalProfit / totalSales) * 100).toFixed(1) : 0;
    wsData.push([
        'TOTAL',
        formatNumberExcel(totalTransactions),
        formatCurrency(totalSales),
        formatCurrency(totalProfit),
        totalMargin + '%'
    ]);

    return XLSX.utils.aoa_to_sheet(wsData);
}

// Create Product Worksheet
function createProductWorksheet(data) {
    const wsData = [];

    // Title and header
    wsData.push(['LAPORAN PENJUALAN PER PRODUK']);
    wsData.push(['Periode: ' + data.summary.period]);
    wsData.push(['Dicetak: ' + data.summary.generated]);
    wsData.push([]); // Empty row

    // Summary section
    wsData.push(['RINGKASAN PENJUALAN']);
    wsData.push(['Total Transaksi', formatNumberExcel(data.summary.total_transactions) + ' transaksi']);
    wsData.push(['Total Penjualan', formatCurrency(data.summary.total_sales)]);
    wsData.push(['Total Laba', formatCurrency(data.summary.total_profit)]);
    wsData.push(['Margin Laba', data.summary.profit_margin + '%']);
    wsData.push([]); // Empty row

    // Product data
    wsData.push(['PENJUALAN PER PRODUK']);
    wsData.push(['No', 'Kode', 'Nama Produk', 'Qty', 'Rata-rata Harga', 'Total Penjualan', 'Total HPP', 'Laba', 'Margin (%)']);

    let totalQty = 0;
    let totalSales = 0;
    let totalCost = 0;
    let totalProfit = 0;

    data.details.forEach((product, index) => {
        const margin = product.total_cost > 0 ? ((product.total_profit / product.total_cost) * 100).toFixed(1) : 0;

        totalQty += parseInt(product.total_qty);
        totalSales += parseFloat(product.total_sales);
        totalCost += parseFloat(product.total_cost);
        totalProfit += parseFloat(product.total_profit);

        wsData.push([
            index + 1,
            product.product_code,
            product.product_name,
            formatNumberExcel(product.total_qty),
            formatCurrency(product.avg_price),
            formatCurrency(product.total_sales),
            formatCurrency(product.total_cost),
            formatCurrency(product.total_profit),
            margin + '%'
        ]);
    });

    // Total row
    const totalMargin = totalCost > 0 ? ((totalProfit / totalCost) * 100).toFixed(1) : 0;
    const avgPrice = totalQty > 0 ? totalSales / totalQty : 0;

    wsData.push([
        '',
        '',
        'TOTAL',
        formatNumberExcel(totalQty),
        formatCurrency(avgPrice),
        formatCurrency(totalSales),
        formatCurrency(totalCost),
        formatCurrency(totalProfit),
        totalMargin + '%'
    ]);

    return XLSX.utils.aoa_to_sheet(wsData);
}

// Create Customer Worksheet
function createCustomerWorksheet(data) {
    const wsData = [];

    // Title and header
    wsData.push(['LAPORAN PENJUALAN PER PELANGGAN']);
    wsData.push(['Periode: ' + data.summary.period]);
    wsData.push(['Dicetak: ' + data.summary.generated]);
    wsData.push([]); // Empty row

    // Summary section
    wsData.push(['RINGKASAN PENJUALAN']);
    wsData.push(['Total Transaksi', formatNumberExcel(data.summary.total_transactions) + ' transaksi']);
    wsData.push(['Total Penjualan', formatCurrency(data.summary.total_sales)]);
    wsData.push(['Total Laba', formatCurrency(data.summary.total_profit)]);
    wsData.push(['Margin Laba', data.summary.profit_margin + '%']);
    wsData.push([]); // Empty row

    // Customer data
    wsData.push(['PENJUALAN PER PELANGGAN']);
    wsData.push(['No', 'Nama Pelanggan', 'Total Transaksi', 'Total Pembelian', 'Rata-rata Transaksi', 'Total Laba', 'Kontribusi (%)']);

    let totalTransactions = 0;
    let totalPurchases = 0;
    let totalProfit = 0;
    const grandTotal = data.details.reduce((sum, customer) => sum + parseFloat(customer.total_purchases), 0);

    data.details.forEach((customer, index) => {
        const contribution = grandTotal > 0 ? ((customer.total_purchases / grandTotal) * 100).toFixed(1) : 0;

        totalTransactions += parseInt(customer.total_transactions);
        totalPurchases += parseFloat(customer.total_purchases);
        totalProfit += parseFloat(customer.total_profit);

        wsData.push([
            index + 1,
            customer.customer_name,
            formatNumberExcel(customer.total_transactions),
            formatCurrency(customer.total_purchases),
            formatCurrency(customer.avg_transaction),
            formatCurrency(customer.total_profit),
            contribution + '%'
        ]);
    });

    // Total row
    const avgTransaction = totalTransactions > 0 ? totalPurchases / totalTransactions : 0;

    wsData.push([
        '',
        'TOTAL',
        formatNumberExcel(totalTransactions),
        formatCurrency(totalPurchases),
        formatCurrency(avgTransaction),
        formatCurrency(totalProfit),
        '100%'
    ]);

    return XLSX.utils.aoa_to_sheet(wsData);
}
