<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    // DataTables parameters
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $searchValue = $_POST['search']['value'];
    
    // Column mapping for ordering
    $columns = [
        0 => 'code',
        1 => 'name',
        2 => 'phone',
        3 => 'email',
        4 => 'is_active'
    ];
    
    // Base query
    $baseQuery = "FROM customers";
    
    // Search condition
    $searchCondition = "";
    $searchParams = [];
    if (!empty($searchValue)) {
        $searchCondition = " WHERE (
            code LIKE ? OR 
            name LIKE ? OR 
            phone LIKE ? OR
            email LIKE ? OR
            address LIKE ?
        )";
        $searchTerm = "%$searchValue%";
        $searchParams = [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }
    
    // Count total records
    $totalRecords = $db->fetchOne("SELECT COUNT(*) as total " . $baseQuery)['total'];
    
    // Count filtered records
    if (!empty($searchValue)) {
        $filteredRecords = $db->fetchOne(
            "SELECT COUNT(*) as total " . $baseQuery . $searchCondition,
            $searchParams
        )['total'];
    } else {
        $filteredRecords = $totalRecords;
    }
    
    // Order condition
    $orderColumn = $columns[$_POST['order'][0]['column']] ?? 'name';
    $orderDirection = $_POST['order'][0]['dir'] === 'desc' ? 'DESC' : 'ASC';
    $orderCondition = " ORDER BY $orderColumn $orderDirection";
    
    // Limit condition
    $limitCondition = " LIMIT $start, $length";
    
    // Main query
    $query = "
        SELECT 
            id,
            code,
            name,
            phone,
            email,
            address,
            is_active,
            created_at,
            (SELECT COUNT(*) FROM sales WHERE customer_id = customers.id) as total_transactions,
            (SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE customer_id = customers.id) as total_purchases
        " . $baseQuery . $searchCondition . $orderCondition . $limitCondition;
    
    $customers = $db->fetchAll($query, $searchParams);
    
    // Format data for DataTables
    $data = [];
    foreach ($customers as $customer) {
        // Status badge
        $statusBadge = $customer['is_active'] 
            ? '<span class="badge bg-success">Aktif</span>'
            : '<span class="badge bg-danger">Tidak Aktif</span>';
        
        // Action buttons
        $actions = '
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary btn-edit" 
                        data-id="' . $customer['id'] . '" 
                        title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-outline-info btn-view" 
                        data-id="' . $customer['id'] . '" 
                        title="Detail">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" class="btn btn-outline-danger btn-delete" 
                        data-id="' . $customer['id'] . '" 
                        title="Hapus">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        ';
        
        $data[] = [
            $customer['code'],
            '<div>
                <strong>' . htmlspecialchars($customer['name']) . '</strong><br>
                <small class="text-muted">' . htmlspecialchars($customer['address'] ?? '') . '</small>
            </div>',
            $customer['phone'] ?: '-',
            $customer['email'] ?: '-',
            '<div class="text-center">
                <div>' . formatRupiah($customer['total_purchases']) . '</div>
                <small class="text-muted">' . $customer['total_transactions'] . ' transaksi</small>
            </div>',
            '<div class="text-center">' . $statusBadge . '</div>',
            '<div class="text-center">' . $actions . '</div>'
        ];
    }
    
    // Response
    $response = [
        'draw' => $draw,
        'recordsTotal' => $totalRecords,
        'recordsFiltered' => $filteredRecords,
        'data' => $data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'draw' => $_POST['draw'] ?? 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ]);
}
?>
