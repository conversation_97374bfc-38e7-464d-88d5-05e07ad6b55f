<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Laporan';
$currentPage = 'reports';
$pageIcon = 'fas fa-chart-bar';
$breadcrumb = [
    ['title' => 'Laporan', 'url' => '#'],
    ['title' => 'Laporan Penjualan']
];

// Default date range (current month)
$defaultFrom = date('Y-m-01');
$defaultTo = date('Y-m-d');

include '../includes/header.php';
?>

<div class="row">
    <!-- Filter Controls -->
    <div class="col-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-filter me-2"></i>
                    Filter La<PERSON>
                </h6>
            </div>
            <div class="card-body">
                <form id="reportForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="reportDateFrom" class="form-label">Dari Tanggal</label>
                            <input type="date" class="form-control" id="reportDateFrom" value="<?php echo $defaultFrom; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="reportDateTo" class="form-label">Sampai Tanggal</label>
                            <input type="date" class="form-control" id="reportDateTo" value="<?php echo $defaultTo; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="reportType" class="form-label">Jenis Laporan</label>
                            <select class="form-select" id="reportType">
                                <option value="summary">Ringkasan</option>
                                <option value="daily">Harian</option>
                                <option value="product">Per Produk</option>
                                <option value="customer">Per Pelanggan</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" id="generateReport">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    Generate Laporan
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row" id="summaryCards" style="display: none;">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Penjualan
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalSales">
                            Rp 0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Laba
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalProfit">
                            Rp 0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Transaksi
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalTransactions">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Margin Laba
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="profitMargin">
                            0%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Content -->
<div class="row">
    <div class="col-12">
        <div class="card shadow" id="reportCard" style="display: none;">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-white" id="reportTitle">
                    <i class="fas fa-chart-bar me-2"></i>
                    Laporan
                </h6>
                <div>
                    <button type="button" class="btn btn-success btn-sm" id="exportExcel" title="Export ke Excel XLSX">
                        <i class="fas fa-file-excel me-1"></i>
                        Export Excel
                    </button>
                    <button type="button" class="btn btn-info btn-sm" id="printReport" title="Print Laporan">
                        <i class="fas fa-print me-1"></i>
                        Print
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="refreshPage()" title="Refresh Laporan">
                        <i class="fas fa-sync-alt me-1"></i>
                        Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="reportContent">
                    <!-- Report content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart Container -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card shadow" id="chartCard" style="display: none;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-chart-pie me-2"></i>
                    Grafik Penjualan
                </h6>
            </div>
            <div class="card-body">
                <canvas id="salesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card shadow" id="profitChartCard" style="display: none;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-chart-line me-2"></i>
                    Grafik Laba
                </h6>
            </div>
            <div class="card-body">
                <canvas id="profitChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Generate report on button click
    $("#generateReport").on("click", function() {
        generateReport();
    });

    // Export Excel
    $("#exportExcel").on("click", function() {
        if (typeof exportToExcel === "function") {
            exportToExcel();
        } else {
            alert("Export function not loaded. Please refresh the page.");
        }
    });

    // Print report
    $("#printReport").on("click", function() {
        printReport();
    });

    // Generate initial report
    generateReport();
});

function generateReport() {
    const dateFrom = $("#reportDateFrom").val();
    const dateTo = $("#reportDateTo").val();
    const reportType = $("#reportType").val();

    if (!dateFrom || !dateTo) {
        showError("Pilih rentang tanggal terlebih dahulu");
        return;
    }

    showLoading("Generating laporan...");

    $.ajax({
        url: "../api/reports.php",
        type: "POST",
        data: {
            date_from: dateFrom,
            date_to: dateTo,
            type: reportType
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                displayReport(response.data);
            } else {
                showError(response.message);
            }
        },
        error: function() {
            hideLoading();
            showError("Gagal generate laporan");
        }
    });
}

function displayReport(data) {
    // Update summary cards
    $("#totalSales").text(formatRupiah(data.summary.total_sales));
    $("#totalProfit").text(formatRupiah(data.summary.total_profit));
    $("#totalTransactions").text(data.summary.total_transactions);
    $("#profitMargin").text(data.summary.profit_margin + "%");

    // Show summary cards
    $("#summaryCards").show();

    // Update report content
    $("#reportContent").html(data.html);
    $("#reportTitle").html("<i class=\"fas fa-chart-bar me-2\"></i>" + data.title);
    $("#reportCard").show();

    // Show charts if data available
    if (data.chart_data) {
        displayCharts(data.chart_data);
        $("#chartCard, #profitChartCard").show();
    }
}

function displayCharts(chartData) {
    // Sales Chart
    const salesCtx = document.getElementById("salesChart").getContext("2d");
    new Chart(salesCtx, {
        type: "line",
        data: {
            labels: chartData.labels,
            datasets: [{
                label: "Penjualan",
                data: chartData.sales,
                borderColor: "rgb(75, 192, 192)",
                backgroundColor: "rgba(75, 192, 192, 0.2)",
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Profit Chart
    const profitCtx = document.getElementById("profitChart").getContext("2d");
    new Chart(profitCtx, {
        type: "bar",
        data: {
            labels: chartData.labels,
            datasets: [{
                label: "Laba",
                data: chartData.profit,
                backgroundColor: "rgba(54, 162, 235, 0.2)",
                borderColor: "rgba(54, 162, 235, 1)",
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Excel Export Implementation using SheetJS

// Export to Excel function
async function exportToExcel() {
    const dateFrom = $("#reportDateFrom").val();
    const dateTo = $("#reportDateTo").val();
    const reportType = $("#reportType").val();

    if (!dateFrom || !dateTo) {
        showError("Pilih rentang tanggal terlebih dahulu");
        return;
    }

    if (!$("#reportCard").is(":visible")) {
        showError("Generate laporan terlebih dahulu sebelum export");
        return;
    }

    try {
        // Check if XLSX is available
        if (typeof XLSX === "undefined") {
            throw new Error("SheetJS library not loaded");
        }

        showLoading("Generating Excel file...");

        // Fetch data from API
        const response = await fetch(`../api/export_data.php?date_from=${dateFrom}&date_to=${dateTo}&type=${reportType}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error);
        }

        // Create simple workbook with summary data
        const wb = XLSX.utils.book_new();
        const wsData = [];

        // Title and summary
        wsData.push(["LAPORAN PENJUALAN"]);
        wsData.push(["Periode: " + result.data.summary.period]);
        wsData.push(["Dicetak: " + result.data.summary.generated]);
        wsData.push([]);
        wsData.push(["RINGKASAN PENJUALAN"]);
        wsData.push(["Total Transaksi", result.data.summary.total_transactions + " transaksi"]);
        wsData.push(["Total Penjualan", "Rp " + new Intl.NumberFormat("id-ID").format(result.data.summary.total_sales)]);
        wsData.push(["Total HPP", "Rp " + new Intl.NumberFormat("id-ID").format(result.data.summary.total_cost)]);
        wsData.push(["Total Laba", "Rp " + new Intl.NumberFormat("id-ID").format(result.data.summary.total_profit)]);
        wsData.push(["Margin Laba", result.data.summary.profit_margin + "%"]);

        // Add detail data based on type
        if (reportType === "summary" && result.data.details.top_products) {
            wsData.push([]);
            wsData.push(["PRODUK TERLARIS"]);
            wsData.push(["No", "Nama Produk", "Qty Terjual", "Total Penjualan", "Total Laba"]);

            result.data.details.top_products.forEach((product, index) => {
                wsData.push([
                    index + 1,
                    product.product_name,
                    new Intl.NumberFormat("id-ID").format(product.total_qty),
                    "Rp " + new Intl.NumberFormat("id-ID").format(product.total_sales),
                    "Rp " + new Intl.NumberFormat("id-ID").format(product.total_profit)
                ]);
            });
        }

        const ws = XLSX.utils.aoa_to_sheet(wsData);
        XLSX.utils.book_append_sheet(wb, ws, "Laporan");

        // Generate filename and save
        const filename = "Laporan_" + reportType + "_" + dateFrom + "_" + dateTo + ".xlsx";

        XLSX.writeFile(wb, filename);

        hideLoading();
        showSuccess("File Excel berhasil didownload");

    } catch (error) {
        hideLoading();
        showError("Error: " + error.message);
    }
}

function printReport() {
    const dateFrom = $("#reportDateFrom").val();
    const dateTo = $("#reportDateTo").val();
    const reportType = $("#reportType").val();

    if (!dateFrom || !dateTo) {
        showError("Pilih rentang tanggal terlebih dahulu");
        return;
    }

    if (!$("#reportCard").is(":visible")) {
        showError("Generate laporan terlebih dahulu sebelum mencetak");
        return;
    }

    // Open print window
    const url = "../api/print_report.php?date_from=" + dateFrom + "&date_to=" + dateTo + "&type=" + reportType;
    window.open(url, "_blank", "width=800,height=600");
}
</script>
';

include '../includes/footer.php';
?>
