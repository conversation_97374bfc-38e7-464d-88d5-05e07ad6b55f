-- Database Schema untuk Aplikasi POS
-- Database: adi

-- Tabel Kategori Produk
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Produk
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    unit VARCHAR(20) DEFAULT 'pcs',
    purchase_price DECIMAL(15,2) NOT NULL DEFAULT 0,
    selling_price DECIMAL(15,2) NOT NULL DEFAULT 0,
    stock_quantity INT DEFAULT 0,
    min_stock INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
);

-- Tabel Pelanggan
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Header Penjualan
CREATE TABLE sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT NULL,
    customer_name VARCHAR(200),
    sale_date DATE NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_cost DECIMAL(15,2) NOT NULL DEFAULT 0,
    profit DECIMAL(15,2) NOT NULL DEFAULT 0,
    notes TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Tabel Detail Penjualan
CREATE TABLE sale_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sale_id INT NOT NULL,
    product_id INT NOT NULL,
    product_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    unit_cost DECIMAL(15,2) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    total_cost DECIMAL(15,2) NOT NULL,
    profit DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
);

-- Insert Data Sample Kategori
INSERT INTO categories (name, description) VALUES
('Kusen Pintu', 'Kusen untuk pintu berbagai ukuran'),
('Kusen Jendela', 'Kusen untuk jendela berbagai ukuran'),
('Daun Pintu', 'Daun pintu kayu dan aluminium'),
('Daun Jendela', 'Daun jendela kaca dan aluminium'),
('Ventilasi', 'Produk ventilasi dan sirkulasi udara'),
('Aksesoris', 'Handle, engsel, kunci dan aksesoris lainnya');

-- Insert Data Sample Produk
INSERT INTO products (category_id, code, name, unit, purchase_price, selling_price, stock_quantity, min_stock) VALUES
(1, 'KP001', 'Kusen Pintu Kayu Jati 80x200', 'set', 450000, 650000, 10, 2),
(1, 'KP002', 'Kusen Pintu Aluminium 80x200', 'set', 350000, 500000, 15, 3),
(2, 'KJ001', 'Kusen Jendela Kayu 60x120', 'set', 250000, 350000, 20, 5),
(2, 'KJ002', 'Kusen Jendela Aluminium 60x120', 'set', 200000, 300000, 25, 5),
(3, 'DP001', 'Daun Pintu Kayu Jati Solid', 'pcs', 800000, 1200000, 8, 2),
(3, 'DP002', 'Daun Pintu Aluminium Kaca', 'pcs', 600000, 900000, 12, 3),
(4, 'DJ001', 'Daun Jendela Kaca Bening 6mm', 'pcs', 150000, 250000, 30, 10),
(4, 'DJ002', 'Daun Jendela Aluminium Sliding', 'pcs', 300000, 450000, 15, 5),
(5, 'VT001', 'Ventilasi Exhaust Fan 12 inch', 'pcs', 180000, 280000, 20, 5),
(5, 'VT002', 'Ventilasi Angin Aluminium', 'pcs', 75000, 125000, 40, 10),
(6, 'AK001', 'Handle Pintu Stainless', 'pcs', 45000, 75000, 50, 15),
(6, 'AK002', 'Engsel Pintu 4 inch', 'pcs', 25000, 40000, 100, 20);

-- Insert Data Sample Pelanggan
INSERT INTO customers (code, name, phone, address) VALUES
('CUST001', 'Umum/Walk-in', '-', '-'),
('CUST002', 'PT. Bangun Jaya', '081234567890', 'Jl. Industri No. 123'),
('CUST003', 'CV. Mitra Konstruksi', '081234567891', 'Jl. Pembangunan No. 456'),
('CUST004', 'Toko Bangunan Sejahtera', '081234567892', 'Jl. Raya Utama No. 789');
