<?php
// Customer Sales Data
$customerData = $db->fetchAll("
    SELECT 
        customer_name,
        COUNT(*) as total_transactions,
        SUM(total_amount) as total_purchases,
        SUM(profit) as total_profit,
        AVG(total_amount) as avg_transaction
    FROM sales
    WHERE sale_date BETWEEN ? AND ?
    GROUP BY customer_id, customer_name
    ORDER BY total_purchases DESC
", [$dateFrom, $dateTo]);
?>

<!-- Customer Sales -->
<div class="summary-section">
    <div class="summary-title">PENJUALAN PER PELANGGAN</div>
    <table class="data-table">
        <thead>
            <tr>
                <th>No</th>
                <th>Nama Pelanggan</th>
                <th>Total Transaksi</th>
                <th>Total Pembelian</th>
                <th>Rata-rata Transaksi</th>
                <th>Total Laba</th>
                <th>Kontribusi (%)</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($customerData)): ?>
                <?php 
                $no = 1;
                $totalTransactions = 0;
                $totalPurchases = 0;
                $totalProfit = 0;
                $grandTotal = array_sum(array_column($customerData, 'total_purchases'));
                ?>
                <?php foreach ($customerData as $customer): ?>
                <?php 
                $contribution = $grandTotal > 0 ? round(($customer['total_purchases'] / $grandTotal) * 100, 1) : 0;
                $totalTransactions += $customer['total_transactions'];
                $totalPurchases += $customer['total_purchases'];
                $totalProfit += $customer['total_profit'];
                ?>
                <tr>
                    <td class="center"><?php echo $no++; ?></td>
                    <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                    <td class="number"><?php echo number_format($customer['total_transactions']); ?></td>
                    <td class="number"><?php echo formatRupiah($customer['total_purchases']); ?></td>
                    <td class="number"><?php echo formatRupiah($customer['avg_transaction']); ?></td>
                    <td class="number"><?php echo formatRupiah($customer['total_profit']); ?></td>
                    <td class="number"><?php echo $contribution; ?>%</td>
                </tr>
                <?php endforeach; ?>
                
                <!-- Total Row -->
                <?php $avgTransaction = $totalTransactions > 0 ? $totalPurchases / $totalTransactions : 0; ?>
                <tr style="background-color: #f0f0f0; font-weight: bold;">
                    <td colspan="2" class="center">TOTAL</td>
                    <td class="number"><?php echo number_format($totalTransactions); ?></td>
                    <td class="number"><?php echo formatRupiah($totalPurchases); ?></td>
                    <td class="number"><?php echo formatRupiah($avgTransaction); ?></td>
                    <td class="number"><?php echo formatRupiah($totalProfit); ?></td>
                    <td class="number">100%</td>
                </tr>
            <?php else: ?>
                <tr>
                    <td colspan="7" class="center">Tidak ada data pelanggan</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
