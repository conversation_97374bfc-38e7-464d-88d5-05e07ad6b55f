<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $db = new Database();
    
    echo "<h2>Outstanding DP Debug Test</h2>";
    
    // Test 1: Check if sales table has data
    echo "<h3>1. Check Sales Table</h3>";
    $allSales = $db->fetchAll("SELECT id, invoice_number, customer_name, payment_status, paid_amount, remaining_amount FROM sales LIMIT 10");
    echo "<pre>";
    echo "Total sales: " . count($allSales) . "\n";
    foreach ($allSales as $sale) {
        echo "ID: {$sale['id']}, Invoice: {$sale['invoice_number']}, Customer: {$sale['customer_name']}, Status: {$sale['payment_status']}, Paid: {$sale['paid_amount']}, Remaining: {$sale['remaining_amount']}\n";
    }
    echo "</pre>";
    
    // Test 2: Check partial payments specifically
    echo "<h3>2. Check Partial Payments</h3>";
    $partialSales = $db->fetchAll("SELECT * FROM sales WHERE payment_status = 'partial'");
    echo "<pre>";
    echo "Partial sales count: " . count($partialSales) . "\n";
    foreach ($partialSales as $sale) {
        echo "ID: {$sale['id']}, Invoice: {$sale['invoice_number']}, Customer: {$sale['customer_name']}, Total: {$sale['total_amount']}, Paid: {$sale['paid_amount']}, Remaining: {$sale['remaining_amount']}\n";
    }
    echo "</pre>";
    
    // Test 3: Check table structure
    echo "<h3>3. Check Sales Table Structure</h3>";
    $columns = $db->fetchAll("DESCRIBE sales");
    echo "<pre>";
    foreach ($columns as $col) {
        echo "Column: {$col['Field']}, Type: {$col['Type']}, Null: {$col['Null']}, Default: {$col['Default']}\n";
    }
    echo "</pre>";
    
    // Test 4: Check if payment_status column exists
    echo "<h3>4. Check Payment Status Values</h3>";
    $statusCount = $db->fetchAll("SELECT payment_status, COUNT(*) as count FROM sales GROUP BY payment_status");
    echo "<pre>";
    foreach ($statusCount as $status) {
        echo "Status: '{$status['payment_status']}', Count: {$status['count']}\n";
    }
    echo "</pre>";
    
    // Test 5: Try to create test data if none exists
    echo "<h3>5. Create Test Data (if needed)</h3>";
    if (count($partialSales) == 0) {
        echo "<p>No partial payments found. Let's check if we can create test data...</p>";
        
        // Check if we have any sales at all
        if (count($allSales) > 0) {
            $firstSale = $allSales[0];
            echo "<p>Found sale ID: {$firstSale['id']}. You can manually update this to partial status for testing.</p>";
            echo "<p>SQL to create test data:</p>";
            echo "<pre>";
            echo "UPDATE sales SET payment_status = 'partial', paid_amount = " . ($firstSale['total_amount'] ?? 100000) * 0.3 . ", remaining_amount = " . ($firstSale['total_amount'] ?? 100000) * 0.7 . " WHERE id = {$firstSale['id']};";
            echo "</pre>";
        } else {
            echo "<p>No sales data found. Please create some sales transactions first through the POS system.</p>";
        }
    }
    
    // Test 6: Test the actual API call
    echo "<h3>6. Test API Response</h3>";
    if (count($partialSales) > 0) {
        echo "<p>Testing outstanding_dp.php API...</p>";
        
        // Simulate API call
        $_GET['action'] = 'list';
        
        ob_start();
        include 'outstanding_dp.php';
        $apiResponse = ob_get_clean();
        
        echo "<pre>";
        echo "API Response: " . $apiResponse;
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
p { margin: 10px 0; }
</style>
