<?php
// Quick timezone test in root folder

echo "<h2>🕐 Quick Timezone Test</h2>";

echo "<h3>1. Current Timezone Status:</h3>";
echo "<p><strong>Before:</strong> " . date_default_timezone_get() . " - " . date('Y-m-d H:i:s T') . "</p>";

date_default_timezone_set('Asia/Makassar');
echo "<p><strong>After setting Makassar:</strong> " . date_default_timezone_get() . " - " . date('Y-m-d H:i:s T') . "</p>";

echo "<h3>2. Format Test:</h3>";
echo "<p><strong>Indonesian Date:</strong> " . date('d/m/Y') . "</p>";
echo "<p><strong>Indonesian DateTime:</strong> " . date('d/m/Y H:i:s') . "</p>";
echo "<p><strong>With Timezone:</strong> " . date('d/m/Y H:i:s T') . "</p>";

echo "<h3>3. Functions Test:</h3>";
try {
    require_once 'includes/functions.php';
    
    $testDate = '2024-12-26';
    $testDateTime = '2024-12-26 14:30:45';
    
    echo "<p><strong>formatDate('2024-12-26'):</strong> " . formatDate($testDate) . "</p>";
    echo "<p><strong>formatDateTime('2024-12-26 14:30:45'):</strong> " . formatDateTime($testDateTime) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Functions Error: " . $e->getMessage() . "</p>";
}

echo "<h3>4. API Files Status:</h3>";
$apiFiles = ['sales.php', 'sale_detail.php', 'sale_delete.php', 'print_receipt.php'];

foreach ($apiFiles as $file) {
    $filePath = __DIR__ . '/api/' . $file;
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        if (strpos($content, "date_default_timezone_set('Asia/Makassar')") !== false) {
            echo "<p>✅ <strong>$file</strong> - Timezone set</p>";
        } else {
            echo "<p>⚠️ <strong>$file</strong> - No timezone setting</p>";
        }
    } else {
        echo "<p>❌ <strong>$file</strong> - File not found</p>";
    }
}

echo "<h3>✅ Summary:</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Current Timezone:</strong> " . date_default_timezone_get() . "</p>";
echo "<p><strong>Current Time:</strong> " . date('d/m/Y H:i:s T') . "</p>";
echo "<p><strong>Status:</strong> Timezone Makassar (WITA) Active</p>";
echo "</div>";

echo "<div style='margin-top: 20px;'>";
echo "<a href='pages/sales.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Riwayat Penjualan</a>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
</style>
