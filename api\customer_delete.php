<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_POST['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID pelanggan diperlukan');
    }
    
    // Check if customer exists
    $customer = $db->fetchOne("SELECT id, name FROM customers WHERE id = ?", [$id]);
    if (!$customer) {
        throw new Exception('Pelanggan tidak ditemukan');
    }
    
    // Check if customer is used in sales
    $usedInSales = $db->fetchOne(
        "SELECT COUNT(*) as total FROM sales WHERE customer_id = ?", 
        [$id]
    )['total'];
    
    if ($usedInSales > 0) {
        throw new Exception('Pelanggan tidak dapat dihapus karena sudah memiliki riwayat transaksi');
    }
    
    $db->beginTransaction();
    
    // Delete the customer
    $db->delete('customers', 'id = ?', [$id]);
    
    $db->commit();
    
    jsonResponse(true, 'Pelanggan "' . $customer['name'] . '" berhasil dihapus');
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
