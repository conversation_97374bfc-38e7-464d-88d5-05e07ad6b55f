<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    // DataTables parameters
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $searchValue = $_POST['search']['value'];

    // Column mapping for ordering
    $columns = [
        0 => 'p.code',
        1 => 'p.name',
        2 => 'c.name',
        3 => 'p.purchase_price',
        4 => 'p.selling_price',
        5 => 'p.stock_quantity',
        6 => 'p.is_active'
    ];

    // Base query
    $baseQuery = "
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
    ";

    // Search condition
    $searchCondition = "";
    $searchParams = [];
    if (!empty($searchValue)) {
        $searchCondition = " WHERE (
            p.code LIKE ? OR
            p.name LIKE ? OR
            c.name LIKE ? OR
            p.description LIKE ?
        )";
        $searchTerm = "%$searchValue%";
        $searchParams = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    // Count total records
    $totalRecords = $db->fetchOne("SELECT COUNT(*) as total " . $baseQuery)['total'];

    // Count filtered records
    if (!empty($searchValue)) {
        $filteredRecords = $db->fetchOne(
            "SELECT COUNT(*) as total " . $baseQuery . $searchCondition,
            $searchParams
        )['total'];
    } else {
        $filteredRecords = $totalRecords;
    }

    // Order condition
    $orderColumn = $columns[$_POST['order'][0]['column']] ?? 'p.id';
    $orderDirection = $_POST['order'][0]['dir'] === 'desc' ? 'DESC' : 'ASC';
    $orderCondition = " ORDER BY $orderColumn $orderDirection";

    // Limit condition
    $limitCondition = " LIMIT $start, $length";

    // Main query
    $query = "
        SELECT
            p.id,
            p.code,
            p.name,
            p.description,
            p.unit,
            p.purchase_price,
            p.selling_price,
            p.stock_quantity,
            p.min_stock,
            p.is_active,
            c.name as category_name,
            (p.selling_price - p.purchase_price) as profit_per_unit,
            CASE
                WHEN p.stock_quantity = 0 THEN 'out_of_stock'
                WHEN p.stock_quantity <= p.min_stock THEN 'low_stock'
                ELSE 'normal'
            END as stock_status
        " . $baseQuery . $searchCondition . $orderCondition . $limitCondition;

    $products = $db->fetchAll($query, $searchParams);

    // Format data for DataTables
    $data = [];
    foreach ($products as $product) {
        // Stock status badge
        $stockBadge = '';
        switch ($product['stock_status']) {
            case 'out_of_stock':
                $stockBadge = '<span class="badge bg-danger">Habis</span>';
                break;
            case 'low_stock':
                $stockBadge = '<span class="badge bg-warning">Rendah</span>';
                break;
            default:
                $stockBadge = '<span class="badge bg-success">Normal</span>';
        }

        // Status badge
        $statusBadge = $product['is_active']
            ? '<span class="badge bg-success">Aktif</span>'
            : '<span class="badge bg-danger">Tidak Aktif</span>';

        // Profit percentage
        $profitPercentage = $product['purchase_price'] > 0
            ? round(($product['profit_per_unit'] / $product['purchase_price']) * 100, 1)
            : 0;

        // Action buttons
        $actions = '
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary btn-edit"
                        data-id="' . $product['id'] . '"
                        title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-outline-info btn-view"
                        data-id="' . $product['id'] . '"
                        title="Detail">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" class="btn btn-outline-danger btn-delete"
                        data-id="' . $product['id'] . '"
                        title="Hapus">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        ';

        $data[] = [
            $product['code'],
            '<div>
                <strong>' . htmlspecialchars($product['name']) . '</strong><br>
                <small class="text-muted">' . htmlspecialchars($product['description'] ?? '') . '</small>
            </div>',
            $product['category_name'],
            '<div class="text-end">' . formatRupiah($product['purchase_price']) . '</div>',
            '<div class="text-end">
                ' . formatRupiah($product['selling_price']) . '<br>
                <small class="text-success">+' . $profitPercentage . '%</small>
            </div>',
            '<div class="text-center">
                <strong>' . number_format($product['stock_quantity']) . '</strong> ' . $product['unit'] . '<br>
                ' . $stockBadge . '
            </div>',
            '<div class="text-center">' . $statusBadge . '</div>',
            '<div class="text-center">' . $actions . '</div>'
        ];
    }

    // Response
    $response = [
        'draw' => $draw,
        'recordsTotal' => $totalRecords,
        'recordsFiltered' => $filteredRecords,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'draw' => $_POST['draw'] ?? 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ]);
}
?>
