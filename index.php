<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$pageTitle = 'Dashboard';
$currentPage = 'dashboard';
$pageIcon = 'fas fa-tachometer-alt';

// Get dashboard statistics
try {
    // Total products
    $totalProducts = $db->fetchOne("SELECT COUNT(*) as total FROM products WHERE is_active = 1")['total'];

    // Total categories
    $totalCategories = $db->fetchOne("SELECT COUNT(*) as total FROM categories")['total'];

    // Total customers
    $totalCustomers = $db->fetchOne("SELECT COUNT(*) as total FROM customers WHERE is_active = 1")['total'];

    // Today's sales
    $todaySales = $db->fetchOne("
        SELECT
            COUNT(*) as total_transactions,
            COALESCE(SUM(total_amount), 0) as total_amount,
            COALESCE(SUM(profit), 0) as total_profit
        FROM sales
        WHERE DATE(sale_date) = CURDATE()
    ");

    // This month's sales
    $monthSales = $db->fetchOne("
        SELECT
            COUNT(*) as total_transactions,
            COALESCE(SUM(total_amount), 0) as total_amount,
            COALESCE(SUM(profit), 0) as total_profit
        FROM sales
        WHERE YEAR(sale_date) = YEAR(CURDATE())
        AND MONTH(sale_date) = MONTH(CURDATE())
    ");

    // Low stock products
    $lowStockProducts = $db->fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.stock_quantity <= p.min_stock
        AND p.is_active = 1
        ORDER BY p.stock_quantity ASC
        LIMIT 10
    ");

    // Recent sales
    $recentSales = $db->fetchAll("
        SELECT s.*, c.name as customer_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        ORDER BY s.created_at DESC
        LIMIT 10
    ");

} catch (Exception $e) {
    $error = $e->getMessage();
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Produk
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($totalProducts); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Penjualan Hari Ini
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo formatRupiah($todaySales['total_amount']); ?>
                        </div>
                        <small class="text-muted"><?php echo $todaySales['total_transactions']; ?> transaksi</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Laba Bulan Ini
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo formatRupiah($monthSales['total_profit']); ?>
                        </div>
                        <small class="text-muted"><?php echo $monthSales['total_transactions']; ?> transaksi</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Stok Rendah
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo count($lowStockProducts); ?>
                        </div>
                        <small class="text-muted">produk</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Low Stock Products -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Produk Stok Rendah
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($lowStockProducts)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>Semua produk memiliki stok yang cukup</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Produk</th>
                                    <th>Kategori</th>
                                    <th>Stok</th>
                                    <th>Min. Stok</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockProducts as $product): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $product['name']; ?></strong><br>
                                        <small class="text-muted"><?php echo $product['code']; ?></small>
                                    </td>
                                    <td><?php echo $product['category_name']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $product['stock_quantity'] == 0 ? 'danger' : 'warning'; ?>">
                                            <?php echo $product['stock_quantity']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $product['min_stock']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Sales -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-receipt me-2"></i>
                    Penjualan Terbaru
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentSales)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-receipt fa-3x mb-3"></i>
                        <p>Belum ada transaksi penjualan</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Invoice</th>
                                    <th>Pelanggan</th>
                                    <th>Total</th>
                                    <th>Tanggal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentSales as $sale): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $sale['invoice_number']; ?></strong>
                                    </td>
                                    <td><?php echo $sale['customer_name'] ?: 'Walk-in'; ?></td>
                                    <td><?php echo formatRupiah($sale['total_amount']); ?></td>
                                    <td><?php echo formatDateTime($sale['created_at']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-bolt me-2"></i>
                    Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="pages/pos.php" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-cash-register fa-2x d-block mb-2"></i>
                            Form Penjualan
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="pages/products.php" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus fa-2x d-block mb-2"></i>
                            Tambah Produk
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="pages/sales.php" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-list fa-2x d-block mb-2"></i>
                            Lihat Penjualan
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="pages/reports.php" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                            Laporan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
