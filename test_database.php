<?php
/**
 * Test Database Connection and Setup
 */

echo "<h2>Testing Database Connection and Setup</h2>";

try {
    // Test database connection
    echo "<h3>1. Testing Database Connection...</h3>";

    $host = 'localhost';
    $username = 'root';
    $password = '8@n90N3!';
    $database = 'adi';

    $mysqli = new mysqli($host, $username, $password);
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    $mysqli->set_charset("utf8mb4");

    echo "✅ Database connection successful!<br>";

    // Check if database exists
    echo "<h3>2. Checking Database...</h3>";
    $result = $mysqli->query("SHOW DATABASES LIKE '$database'");
    if ($result->num_rows == 0) {
        echo "❌ Database '$database' does not exist. Creating...<br>";
        $mysqli->query("CREATE DATABASE $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Database '$database' created successfully!<br>";
    } else {
        echo "✅ Database '$database' exists!<br>";
    }

    // Connect to the specific database
    $mysqli->close();
    $mysqli = new mysqli($host, $username, $password, $database);
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    $mysqli->set_charset("utf8mb4");

    // Check tables
    echo "<h3>3. Checking Tables...</h3>";
    $tables = ['categories', 'products', 'customers', 'sales', 'sale_items'];

    foreach ($tables as $table) {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows == 0) {
            echo "❌ Table '$table' does not exist<br>";
        } else {
            echo "✅ Table '$table' exists<br>";
        }
    }

    // Test config/database.php
    echo "<h3>4. Testing Application Database Class...</h3>";
    require_once 'config/database.php';

    $testQuery = $db->fetchOne("SELECT 1 as test");
    if ($testQuery['test'] == 1) {
        echo "✅ Application database class working!<br>";
    }

    // Test sample data
    echo "<h3>5. Checking Sample Data...</h3>";

    $categoryCount = $db->fetchOne("SELECT COUNT(*) as total FROM categories")['total'];
    echo "Categories: $categoryCount records<br>";

    $productCount = $db->fetchOne("SELECT COUNT(*) as total FROM products")['total'];
    echo "Products: $productCount records<br>";

    $customerCount = $db->fetchOne("SELECT COUNT(*) as total FROM customers")['total'];
    echo "Customers: $customerCount records<br>";

    echo "<h3>6. Sample Products:</h3>";
    $products = $db->fetchAll("
        SELECT p.code, p.name, c.name as category, p.selling_price
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LIMIT 5
    ");

    if (!empty($products)) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Code</th><th>Name</th><th>Category</th><th>Price</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['code']) . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['category']) . "</td>";
            echo "<td>Rp " . number_format($product['selling_price'], 0, ',', '.') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No products found. Please run the database_schema.sql file first.";
    }

    echo "<h3>✅ Database test completed successfully!</h3>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>If tables don't exist, import the database_schema.sql file into your database</li>";
    echo "<li>Access the application at: <a href='index.php'>index.php</a></li>";
    echo "<li>Go to Products page: <a href='pages/products.php'>pages/products.php</a></li>";
    echo "</ul>";

} catch (mysqli_sql_exception $e) {
    echo "<h3>❌ Database Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>Please check:</strong></p>";
    echo "<ul>";
    echo "<li>AppServ/MySQL is running</li>";
    echo "<li>Database credentials are correct</li>";
    echo "<li>Database 'adi' exists or can be created</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<h3>❌ Application Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
