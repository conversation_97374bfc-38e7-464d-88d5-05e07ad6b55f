<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once '../config/database.php';

try {
    $db = new Database();
    
    echo "<h2>Database Structure Check</h2>";
    
    // Check sales table structure
    echo "<h3>Sales Table Structure:</h3>";
    $salesColumns = $db->fetchAll("DESCRIBE sales");
    
    $requiredColumns = ['payment_status', 'paid_amount', 'remaining_amount'];
    $missingColumns = [];
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th><th>Status</th></tr>";
    
    $existingColumns = [];
    foreach ($salesColumns as $col) {
        $existingColumns[] = $col['Field'];
        $status = in_array($col['Field'], $requiredColumns) ? '✅ Required' : '';
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for missing columns
    foreach ($requiredColumns as $col) {
        if (!in_array($col, $existingColumns)) {
            $missingColumns[] = $col;
        }
    }
    
    if (!empty($missingColumns)) {
        echo "<h3 style='color: red;'>Missing Required Columns:</h3>";
        echo "<ul>";
        foreach ($missingColumns as $col) {
            echo "<li style='color: red;'>{$col}</li>";
        }
        echo "</ul>";
        
        echo "<h3>SQL to Add Missing Columns:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        
        if (in_array('payment_status', $missingColumns)) {
            echo "ALTER TABLE sales ADD COLUMN payment_status ENUM('paid', 'partial') DEFAULT 'paid';\n";
        }
        if (in_array('paid_amount', $missingColumns)) {
            echo "ALTER TABLE sales ADD COLUMN paid_amount DECIMAL(15,2) DEFAULT 0;\n";
        }
        if (in_array('remaining_amount', $missingColumns)) {
            echo "ALTER TABLE sales ADD COLUMN remaining_amount DECIMAL(15,2) DEFAULT 0;\n";
        }
        
        echo "</pre>";
        
        echo "<p><strong>Please run the above SQL commands in your database to add the missing columns.</strong></p>";
    } else {
        echo "<h3 style='color: green;'>✅ All required columns exist!</h3>";
    }
    
    // Check sale_payments table
    echo "<h3>Sale Payments Table:</h3>";
    $tableExists = false;
    try {
        $paymentColumns = $db->fetchAll("DESCRIBE sale_payments");
        $tableExists = true;
        
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr>";
        foreach ($paymentColumns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ sale_payments table does not exist!</p>";
        
        echo "<h3>SQL to Create sale_payments Table:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo "CREATE TABLE sale_payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sale_id INT NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_type ENUM('dp', 'installment', 'full') NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sale_id) REFERENCES sales(id)
);";
        echo "</pre>";
    }
    
    // Check sample data
    echo "<h3>Sample Data Check:</h3>";
    $sampleSales = $db->fetchAll("SELECT id, invoice_number, customer_name, total_amount, payment_status, paid_amount, remaining_amount FROM sales LIMIT 5");
    
    if (count($sampleSales) > 0) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Invoice</th><th>Customer</th><th>Total</th><th>Status</th><th>Paid</th><th>Remaining</th></tr>";
        foreach ($sampleSales as $sale) {
            echo "<tr>";
            echo "<td>{$sale['id']}</td>";
            echo "<td>{$sale['invoice_number']}</td>";
            echo "<td>{$sale['customer_name']}</td>";
            echo "<td>" . number_format($sale['total_amount']) . "</td>";
            echo "<td>{$sale['payment_status']}</td>";
            echo "<td>" . number_format($sale['paid_amount']) . "</td>";
            echo "<td>" . number_format($sale['remaining_amount']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>No sales data found. Please create some transactions first.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
table { margin: 10px 0; }
th { background: #f0f0f0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
