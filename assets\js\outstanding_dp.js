/**
 * Outstanding DP Management JavaScript
 */

let outstandingData = [];
let currentSaleId = null;

// Check if jQuery is loaded
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded! Outstanding DP functionality will not work.');
    alert('Error: jQuery library tidak dimuat. <PERSON><PERSON>an refresh halaman.');
} else {
    console.log('jQuery loaded successfully, version:', jQuery.fn.jquery);
}

// Check if required functions are available
if (typeof formatRupiah === 'undefined') {
    console.warn('formatRupiah function not found, creating fallback');
    window.formatRupiah = function(amount) {
        return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount || 0);
    };
}

if (typeof formatNumber === 'undefined') {
    console.warn('formatNumber function not found, creating fallback');
    window.formatNumber = function(number) {
        return new Intl.NumberFormat('id-ID').format(number || 0);
    };
}

if (typeof parseNumber === 'undefined') {
    console.warn('parseNumber function not found, creating fallback');
    window.parseNumber = function(formattedNumber) {
        if (!formattedNumber) return 0;
        return parseInt(String(formattedNumber).replace(/[^\d]/g, '')) || 0;
    };
}

if (typeof showError === 'undefined') {
    console.warn('showError function not found, creating fallback');
    window.showError = function(message) {
        console.error('Error:', message);
        alert('Error: ' + message);
    };
}

if (typeof showSuccess === 'undefined') {
    console.warn('showSuccess function not found, creating fallback');
    window.showSuccess = function(message) {
        console.log('Success:', message);
        alert('Success: ' + message);
    };
}

if (typeof showLoading === 'undefined') {
    console.warn('showLoading function not found, creating fallback');
    window.showLoading = function(message) {
        console.log('Loading:', message);
    };
}

if (typeof hideLoading === 'undefined') {
    console.warn('hideLoading function not found, creating fallback');
    window.hideLoading = function() {
        console.log('Loading hidden');
    };
}

$(document).ready(function() {
    console.log('Outstanding DP: Document ready');

    // Check for highlight parameter
    const urlParams = new URLSearchParams(window.location.search);
    const highlightId = urlParams.get('highlight');

    // Load initial data
    loadOutstandingData();
    loadSummaryData();

    // Event handlers
    $('#searchInput').on('input', debounce(filterData, 300));
    $('#statusFilter, #sortBy').on('change', filterData);
    $('#refreshData').on('click', refreshAllData);
    $('#exportOutstanding').on('click', exportToExcel);
    $('#processPayment').on('click', processInstallment);

    // Payment amount calculation
    $('#installmentAmount').on('input', calculateInstallmentSummary);

    // Auto-open payment modal if highlight parameter exists
    if (highlightId) {
        setTimeout(function() {
            highlightAndOpenPayment(highlightId);
        }, 1000); // Wait for data to load
    }

    console.log('Outstanding DP: All event handlers attached');
});

/**
 * Load outstanding DP data
 */
function loadOutstandingData() {
    const search = $('#searchInput').val();
    const status = $('#statusFilter').val();
    const sort = $('#sortBy').val();

    console.log('Loading outstanding data with params:', { search, status, sort });

    $.ajax({
        url: '../api/outstanding_dp.php',
        type: 'GET',
        data: {
            action: 'list',
            search: search,
            status: status,
            sort: sort
        },
        success: function(response) {
            console.log('Outstanding API Response:', response);

            if (response.success) {
                outstandingData = response.data;
                console.log('Outstanding data loaded:', outstandingData);
                renderOutstandingTable();
            } else {
                console.error('API Error:', response.message);
                showError(response.message);
                renderOutstandingTable(); // Render empty table
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', { xhr, status, error });
            console.error('Response Text:', xhr.responseText);
            showError('Gagal memuat data outstanding DP: ' + error);
            renderOutstandingTable(); // Render empty table
        }
    });
}

/**
 * Load summary data
 */
function loadSummaryData() {
    console.log('Loading summary data...');

    $.ajax({
        url: '../api/outstanding_dp.php',
        type: 'GET',
        data: { action: 'summary' },
        success: function(response) {
            console.log('Summary API Response:', response);

            if (response.success) {
                updateSummaryCards(response.data);
            } else {
                console.error('Summary API Error:', response.message);
                // Set default values
                updateSummaryCards({
                    total_outstanding: 0,
                    total_customers: 0,
                    average_remaining: 0,
                    total_paid: 0
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Summary AJAX Error:', { xhr, status, error });
            console.error('Summary Response Text:', xhr.responseText);

            // Set default values
            updateSummaryCards({
                total_outstanding: 0,
                total_customers: 0,
                average_remaining: 0,
                total_paid: 0
            });
        }
    });
}

/**
 * Update summary cards
 */
function updateSummaryCards(data) {
    $('#totalOutstanding').text(formatRupiah(data.total_outstanding));
    $('#totalCustomers').text(data.total_customers);
    $('#averageRemaining').text(formatRupiah(data.average_remaining));
    $('#totalPaid').text(formatRupiah(data.total_paid));
}

/**
 * Render outstanding table
 */
function renderOutstandingTable() {
    const tbody = $('#outstandingTableBody');
    tbody.empty();

    if (outstandingData.length === 0) {
        tbody.html(`
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x mb-2 text-muted"></i>
                    <p class="text-muted">Tidak ada data outstanding DP</p>
                </td>
            </tr>
        `);
        return;
    }

    outstandingData.forEach(function(item) {
        const progressColor = getProgressColor(item.payment_progress);

        const row = `
            <tr>
                <td>
                    <strong>${item.customer_name}</strong>
                </td>
                <td>
                    <span class="badge bg-primary">${item.invoice_number}</span>
                </td>
                <td>${item.sale_date}</td>
                <td>${formatRupiah(item.total_amount)}</td>
                <td>${formatRupiah(item.paid_amount)}</td>
                <td>
                    <strong class="text-danger">${formatRupiah(item.remaining_amount)}</strong>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar ${progressColor}"
                             style="width: ${item.payment_progress}%">
                            ${item.payment_progress}%
                        </div>
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-success btn-payment"
                                data-id="${item.id}"
                                title="Bayar Cicilan">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                        <button class="btn btn-info btn-detail"
                                data-id="${item.id}"
                                title="Lihat Detail">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });

    // Bind event handlers
    $('.btn-payment').on('click', function() {
        const saleId = $(this).data('id');
        showPaymentModal(saleId);
    });

    $('.btn-detail').on('click', function() {
        const saleId = $(this).data('id');
        showDetailModal(saleId);
    });
}

/**
 * Get progress bar color based on percentage
 */
function getProgressColor(progress) {
    if (progress < 30) return 'bg-danger';
    if (progress < 70) return 'bg-warning';
    return 'bg-success';
}

/**
 * Show payment modal
 */
function showPaymentModal(saleId) {
    currentSaleId = saleId;
    const item = outstandingData.find(item => item.id == saleId);

    if (!item) {
        showError('Data tidak ditemukan');
        return;
    }

    // Build payment details
    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Customer:</strong></td><td>${item.customer_name}</td></tr>
                    <tr><td><strong>Invoice:</strong></td><td>${item.invoice_number}</td></tr>
                    <tr><td><strong>Tanggal:</strong></td><td>${item.sale_date}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Total:</strong></td><td>${formatRupiah(item.total_amount)}</td></tr>
                    <tr><td><strong>Sudah Dibayar:</strong></td><td>${formatRupiah(item.paid_amount)}</td></tr>
                    <tr><td><strong>Sisa Bayar:</strong></td><td class="text-danger"><strong>${formatRupiah(item.remaining_amount)}</strong></td></tr>
                </table>
            </div>
        </div>
    `;

    $('#paymentDetails').html(detailsHtml);
    $('#installmentAmount').val('').attr('max', item.remaining_amount);
    $('#installmentNotes').val('');
    $('#paymentSummary').html('');

    $('#paymentModal').modal('show');
}

/**
 * Calculate installment summary
 */
function calculateInstallmentSummary() {
    if (!currentSaleId) return;

    const item = outstandingData.find(item => item.id == currentSaleId);
    const paymentAmount = parseNumber($('#installmentAmount').val());

    if (!paymentAmount || paymentAmount <= 0) {
        $('#paymentSummary').html('');
        return;
    }

    if (paymentAmount > item.remaining_amount) {
        $('#paymentSummary').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Jumlah pembayaran melebihi sisa tagihan!
            </div>
        `);
        return;
    }

    const newRemaining = item.remaining_amount - paymentAmount;
    const isFullPayment = newRemaining <= 0;

    const summaryHtml = `
        <div class="alert ${isFullPayment ? 'alert-success' : 'alert-info'}">
            <h6><i class="fas fa-calculator me-2"></i>Ringkasan Pembayaran:</h6>
            <table class="table table-sm table-borderless mb-0">
                <tr><td>Jumlah Bayar:</td><td><strong>${formatRupiah(paymentAmount)}</strong></td></tr>
                <tr><td>Sisa Setelah Bayar:</td><td><strong>${formatRupiah(newRemaining)}</strong></td></tr>
                <tr><td>Status:</td><td><strong class="${isFullPayment ? 'text-success' : 'text-warning'}">${isFullPayment ? 'LUNAS' : 'BELUM LUNAS'}</strong></td></tr>
            </table>
        </div>
    `;

    $('#paymentSummary').html(summaryHtml);
}

/**
 * Process installment payment
 */
function processInstallment() {
    if (!currentSaleId) {
        showError('Sale ID tidak ditemukan');
        return;
    }

    const paymentAmount = parseNumber($('#installmentAmount').val());
    const notes = $('#installmentNotes').val();

    console.log('Processing installment:', {
        sale_id: currentSaleId,
        payment_amount: paymentAmount,
        notes: notes
    });

    if (!paymentAmount || paymentAmount <= 0) {
        showError('Masukkan jumlah pembayaran yang valid');
        return;
    }

    const item = outstandingData.find(item => item.id == currentSaleId);
    if (!item) {
        showError('Data transaksi tidak ditemukan');
        return;
    }

    if (paymentAmount > item.remaining_amount) {
        showError('Jumlah pembayaran melebihi sisa tagihan');
        return;
    }

    showLoading('Memproses pembayaran...');

    const requestData = {
        sale_id: parseInt(currentSaleId),
        payment_amount: parseFloat(paymentAmount),
        notes: notes || ''
    };

    console.log('Sending request data:', requestData);

    $.ajax({
        url: '../api/process_installment.php',
        type: 'POST',
        data: JSON.stringify(requestData),
        contentType: 'application/json',
        success: function(response) {
            hideLoading();

            console.log('Payment response:', response);

            if (response.success) {
                const data = response.data;

                showSuccess(response.message);

                // Show success details
                Swal.fire({
                    title: data.is_fully_paid ? 'Pembayaran Lunas!' : 'Pembayaran Berhasil!',
                    html: `
                        <div class="text-start">
                            <p><strong>Customer:</strong> ${data.customer_name}</p>
                            <p><strong>Invoice:</strong> ${data.invoice_number}</p>
                            <p><strong>Jumlah Bayar:</strong> ${formatRupiah(data.payment_amount)}</p>
                            <p><strong>Sisa Tagihan:</strong> ${formatRupiah(data.new_remaining_amount)}</p>
                            <p><strong>Status:</strong> <span class="${data.is_fully_paid ? 'text-success' : 'text-warning'}">${data.is_fully_paid ? 'LUNAS' : 'BELUM LUNAS'}</span></p>
                        </div>
                    `,
                    icon: 'success',
                    confirmButtonText: 'OK'
                });

                // Close modal and refresh data
                $('#paymentModal').modal('hide');
                refreshAllData();

            } else {
                console.error('Payment API Error:', response.message);
                showError(response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            console.error('Payment AJAX Error:', { xhr, status, error });
            console.error('Response Text:', xhr.responseText);

            let errorMessage = 'Gagal memproses pembayaran';

            // Try to parse error response
            try {
                const errorResponse = JSON.parse(xhr.responseText);
                if (errorResponse.message) {
                    errorMessage = errorResponse.message;
                }
            } catch (e) {
                // If not JSON, show generic error
                errorMessage += ': ' + error;
            }

            showError(errorMessage);
        }
    });
}

/**
 * Show detail modal
 */
function showDetailModal(saleId) {
    showLoading('Memuat detail...');

    $.ajax({
        url: '../api/outstanding_dp.php',
        type: 'GET',
        data: {
            action: 'detail',
            id: saleId
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                renderDetailModal(response.data);
                $('#detailModal').modal('show');
            } else {
                showError(response.message);
            }
        },
        error: function() {
            hideLoading();
            showError('Gagal memuat detail transaksi');
        }
    });
}

/**
 * Render detail modal content
 */
function renderDetailModal(data) {
    const sale = data.sale;
    const items = data.items;
    const payments = data.payments;

    let itemsHtml = '';
    items.forEach(function(item) {
        itemsHtml += `
            <tr>
                <td>${item.product_name}<br><small class="text-muted">${item.product_code}</small></td>
                <td class="text-center">${item.quantity}</td>
                <td class="text-end">${formatRupiah(item.unit_price)}</td>
                <td class="text-end">${formatRupiah(item.discount_amount)}</td>
                <td class="text-end">${formatRupiah(item.final_price)}</td>
                <td class="text-end"><strong>${formatRupiah(item.total_price)}</strong></td>
            </tr>
        `;
    });

    let paymentsHtml = '';
    payments.forEach(function(payment, index) {
        const badgeClass = payment.payment_type === 'dp' ? 'bg-warning' :
                          payment.payment_type === 'full' ? 'bg-success' : 'bg-info';

        paymentsHtml += `
            <tr>
                <td>${index + 1}</td>
                <td>${payment.payment_date}</td>
                <td><span class="badge ${badgeClass}">${payment.payment_type.toUpperCase()}</span></td>
                <td class="text-end"><strong>${formatRupiah(payment.amount)}</strong></td>
                <td>${payment.notes || '-'}</td>
            </tr>
        `;
    });

    const progressColor = getProgressColor(sale.payment_progress);

    const modalContent = `
        <!-- Transaction Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h6><i class="fas fa-receipt me-2"></i>Informasi Transaksi</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Invoice:</strong></td><td>${sale.invoice_number}</td></tr>
                    <tr><td><strong>Customer:</strong></td><td>${sale.customer_name}</td></tr>
                    <tr><td><strong>Tanggal:</strong></td><td>${sale.sale_date}</td></tr>
                    <tr><td><strong>Catatan:</strong></td><td>${sale.notes || '-'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-money-bill-wave me-2"></i>Informasi Pembayaran</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Total Transaksi:</strong></td><td>${formatRupiah(sale.total_amount)}</td></tr>
                    <tr><td><strong>Sudah Dibayar:</strong></td><td class="text-success">${formatRupiah(sale.paid_amount)}</td></tr>
                    <tr><td><strong>Sisa Bayar:</strong></td><td class="text-danger"><strong>${formatRupiah(sale.remaining_amount)}</strong></td></tr>
                </table>
                <div class="progress mb-2" style="height: 25px;">
                    <div class="progress-bar ${progressColor}" style="width: ${sale.payment_progress}%">
                        ${sale.payment_progress}% Terbayar
                    </div>
                </div>
            </div>
        </div>

        <!-- Items -->
        <h6><i class="fas fa-shopping-cart me-2"></i>Detail Produk</h6>
        <div class="table-responsive mb-4">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Produk</th>
                        <th class="text-center">Qty</th>
                        <th class="text-end">Harga</th>
                        <th class="text-end">Diskon</th>
                        <th class="text-end">Harga Final</th>
                        <th class="text-end">Total</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHtml}
                </tbody>
            </table>
        </div>

        <!-- Payment History -->
        <h6><i class="fas fa-history me-2"></i>History Pembayaran</h6>
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Tanggal</th>
                        <th>Jenis</th>
                        <th class="text-end">Jumlah</th>
                        <th>Catatan</th>
                    </tr>
                </thead>
                <tbody>
                    ${paymentsHtml}
                </tbody>
            </table>
        </div>
    `;

    $('#detailModalBody').html(modalContent);
}

/**
 * Filter data
 */
function filterData() {
    loadOutstandingData();
}

/**
 * Refresh all data
 */
function refreshAllData() {
    loadOutstandingData();
    loadSummaryData();
    showSuccess('Data berhasil direfresh');
}

/**
 * Export to Excel
 */
function exportToExcel() {
    if (outstandingData.length === 0) {
        showError('Tidak ada data untuk diekspor');
        return;
    }

    showLoading('Generating Excel file...');

    try {
        // Check if XLSX is available
        if (typeof XLSX === "undefined") {
            throw new Error("SheetJS library not loaded");
        }

        // Create workbook
        const wb = XLSX.utils.book_new();
        const wsData = [];

        // Title and headers
        wsData.push(["OUTSTANDING DP REPORT"]);
        wsData.push(["Generated: " + new Date().toLocaleString('id-ID')]);
        wsData.push([]);
        wsData.push(["Customer", "Invoice", "Tanggal", "Total Transaksi", "Sudah Dibayar", "Sisa Bayar", "Progress (%)"]);

        // Data rows
        outstandingData.forEach(function(item) {
            wsData.push([
                item.customer_name,
                item.invoice_number,
                item.sale_date,
                item.total_amount,
                item.paid_amount,
                item.remaining_amount,
                item.payment_progress
            ]);
        });

        // Summary
        wsData.push([]);
        wsData.push(["SUMMARY"]);
        wsData.push(["Total Customers", outstandingData.length]);
        wsData.push(["Total Outstanding", outstandingData.reduce((sum, item) => sum + item.remaining_amount, 0)]);

        const ws = XLSX.utils.aoa_to_sheet(wsData);
        XLSX.utils.book_append_sheet(wb, ws, "Outstanding DP");

        // Generate filename and save
        const filename = "Outstanding_DP_" + new Date().toISOString().slice(0, 10) + ".xlsx";
        XLSX.writeFile(wb, filename);

        hideLoading();
        showSuccess("File Excel berhasil didownload");

    } catch (error) {
        hideLoading();
        showError("Error: " + error.message);
    }
}

/**
 * Highlight and open payment modal for specific transaction
 */
function highlightAndOpenPayment(saleId) {
    const item = outstandingData.find(item => item.id == saleId);

    if (item) {
        // Highlight the row
        const row = $(`.btn-payment[data-id="${saleId}"]`).closest('tr');
        row.addClass('table-warning');

        // Scroll to the row
        if (row.length > 0) {
            $('html, body').animate({
                scrollTop: row.offset().top - 100
            }, 500);
        }

        // Open payment modal after a short delay
        setTimeout(function() {
            showPaymentModal(saleId);
        }, 500);

        // Remove highlight after modal is shown
        setTimeout(function() {
            row.removeClass('table-warning');
        }, 2000);
    } else {
        showError('Transaksi tidak ditemukan atau sudah lunas');
    }
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
