<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID produk diperlukan');
    }
    
    $product = $db->fetchOne("
        SELECT 
            p.*,
            c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.id = ?
    ", [$id]);
    
    if (!$product) {
        throw new Exception('Produk tidak ditemukan');
    }
    
    jsonResponse(true, 'Data produk berhasil diambil', $product);
    
} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
