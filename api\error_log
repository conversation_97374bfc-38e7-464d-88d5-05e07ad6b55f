[26-May-2025 08:13:06 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 08:13:06 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 08:13:06 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 08:13:06 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 08:13:06 Asia/Makassar] Outstanding Params: []
[26-May-2025 08:13:06 Asia/Makassar] Outstanding Results Count: 0
[26-May-2025 08:14:08 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 08:14:08 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 08:14:08 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 08:14:08 Asia/Makassar] Outstanding Params: []
[26-May-2025 08:14:08 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 08:14:08 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 15:29:43 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 15:29:43 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 15:29:43 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 15:29:43 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 15:29:43 Asia/Makassar] Outstanding Params: []
[26-May-2025 15:29:43 Asia/Makassar] Outstanding Results Count: 2
[26-May-2025 15:30:05 Asia/Makassar] Process Installment - Sale ID: 10, Amount: 300000
[26-May-2025 15:30:05 Asia/Makassar] Updating sale ID 10: paid=1300000, remaining=0, status=paid
[26-May-2025 15:30:05 Asia/Makassar] Update result: 1 rows affected
[26-May-2025 15:30:05 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 15:30:05 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 15:30:05 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 15:30:05 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 15:30:05 Asia/Makassar] Outstanding Params: []
[26-May-2025 15:30:05 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 15:30:36 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 15:30:36 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 15:30:36 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 15:30:36 Asia/Makassar] Outstanding Params: []
[26-May-2025 15:30:36 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 15:30:36 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 15:39:10 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 15:39:10 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 15:39:10 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 15:39:10 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 15:39:10 Asia/Makassar] Outstanding Params: []
[26-May-2025 15:39:10 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 15:39:22 Asia/Makassar] Outstanding DP API - Action: detail
[26-May-2025 18:55:38 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 18:55:38 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 18:55:38 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 18:55:38 Asia/Makassar] Outstanding Params: []
[26-May-2025 18:55:38 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 18:55:38 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 18:56:07 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 18:56:07 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 18:56:07 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 18:56:07 Asia/Makassar] Outstanding Params: []
[26-May-2025 18:56:07 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 18:56:07 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 18:59:02 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 18:59:02 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 18:59:02 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 18:59:02 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 18:59:02 Asia/Makassar] Outstanding Params: []
[26-May-2025 18:59:02 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 18:59:49 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 18:59:49 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 18:59:49 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 18:59:49 Asia/Makassar] Outstanding Params: []
[26-May-2025 18:59:49 Asia/Makassar] Outstanding Results Count: 1
[26-May-2025 18:59:49 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 19:03:24 Asia/Makassar] Outstanding DP API - Action: summary
[26-May-2025 19:03:24 Asia/Makassar] Outstanding DP API - Action: list
[26-May-2025 19:03:24 Asia/Makassar] Outstanding List - Search: '', Status: '', Sort: 'sale_date DESC'
[26-May-2025 19:03:24 Asia/Makassar] Outstanding Query: 
        SELECT
            s.id,
            s.invoice_number,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.paid_amount,
            s.remaining_amount,
            s.created_at,
            ROUND((s.paid_amount / s.total_amount) * 100, 1) as payment_progress
        FROM sales s
        WHERE s.payment_status = 'partial'
        ORDER BY sale_date DESC
    
[26-May-2025 19:03:24 Asia/Makassar] Outstanding Params: []
[26-May-2025 19:03:24 Asia/Makassar] Outstanding Results Count: 1
