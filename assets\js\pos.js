/**
 * Point of Sale JavaScript
 */

let cart = [];
let products = [];

// Fallback functions if not loaded from app.js
if (typeof formatRupiah === 'undefined') {
    function formatRupiah(amount) {
        if (isNaN(amount) || amount === null || amount === undefined) {
            return 'Rp 0';
        }
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(amount);
    }
}

if (typeof parseNumber === 'undefined') {
    function parseNumber(formattedNumber) {
        if (!formattedNumber) return 0;
        return parseInt(String(formattedNumber).replace(/[^\d]/g, '')) || 0;
    }
}

if (typeof formatNumber === 'undefined') {
    function formatNumber(number) {
        if (isNaN(number) || number === null || number === undefined) {
            return '0';
        }
        return new Intl.NumberFormat('id-ID').format(number);
    }
}

$(document).ready(function() {
    // Debug: Check if required functions exist
    console.log('formatRupiah function:', typeof formatRupiah);
    console.log('parseNumber function:', typeof parseNumber);

    // Load products on page load
    loadProducts();

    // Product search
    $('#productSearch').on('input', debounce(function() {
        loadProducts();
    }, 300));

    // Category filter
    $('#categoryFilter').on('change', function() {
        loadProducts();
    });

    // Payment amount calculation
    $('#paymentAmount').on('input', function() {
        calculateChange();
    });

    // Payment type change handler
    $('input[name="paymentType"]').on('change', function() {
        const paymentType = $(this).val();
        handlePaymentTypeChange(paymentType);
    });

    // Quick customer form
    $('#quickCustomerForm').on('submit', function(e) {
        e.preventDefault();
        addQuickCustomer();
    });

    // Cart event delegation - set up once to avoid re-binding issues
    $(document).on('change input', '.qty-input', function() {
        const productId = $(this).data('product-id');
        const quantity = parseInt($(this).val());
        updateCartQuantity(productId, quantity);
    });

    $(document).on('input', '.discount-input', function() {
        // Format input as currency
        let value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = formatNumber(parseInt(value));
        }

        const productId = $(this).data('product-id');
        const discountAmount = parseNumber($(this).val());
        updateItemDiscountSilent(productId, discountAmount);
    });

    $(document).on('change', '.discount-input', function() {
        const productId = $(this).data('product-id');
        const discountAmount = parseNumber($(this).val());
        updateItemDiscount(productId, discountAmount);
    });

    $(document).on('click', '.remove-item', function() {
        const productId = $(this).data('product-id');
        removeFromCart(productId);
    });

    // Process payment
    $('#processPayment').on('click', function() {
        if (cart.length === 0) {
            showError('Keranjang masih kosong');
            return;
        }
        showPaymentModal();
    });

    // Complete sale
    $('#completeSale').on('click', function() {
        completeSale();
    });

    // Clear cart
    $('#clearCart').on('click', function() {
        if (cart.length > 0) {
            confirmAction('Kosongkan Keranjang', 'Yakin ingin mengosongkan keranjang?', function() {
                clearCart();
            });
        }
    });
});

/**
 * Load products from server
 */
function loadProducts() {
    $('#productLoading').show();
    $('#productGrid').hide();

    const search = $('#productSearch').val();
    const category = $('#categoryFilter').val();

    $.ajax({
        url: '../api/pos_products.php',
        type: 'GET',
        data: {
            search: search,
            category: category
        },
        success: function(response) {
            if (response.success) {
                products = response.data;
                renderProducts();
            } else {
                showError(response.message);
            }
        },
        error: function() {
            showError('Gagal memuat produk');
        },
        complete: function() {
            $('#productLoading').hide();
            $('#productGrid').show();
        }
    });
}

/**
 * Render products grid
 */
function renderProducts() {
    const grid = $('#productGrid');
    grid.empty();

    if (products.length === 0) {
        grid.html(`
            <div class="col-12 text-center py-4">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <p class="text-muted">Tidak ada produk ditemukan</p>
            </div>
        `);
        return;
    }

    products.forEach(function(product) {
        const stockBadge = product.stock_quantity <= product.min_stock
            ? '<span class="badge bg-warning">Stok Rendah</span>'
            : '<span class="badge bg-success">Tersedia</span>';

        const isOutOfStock = product.stock_quantity === 0;
        const buttonClass = isOutOfStock ? 'btn-secondary' : 'btn-primary';
        const buttonText = isOutOfStock ? 'Stok Habis' : 'Tambah';
        const buttonDisabled = isOutOfStock ? 'disabled' : '';

        const productCard = `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100 product-card" data-product-id="${product.id}">
                    <div class="card-body">
                        <h6 class="card-title">${product.name}</h6>
                        <p class="card-text">
                            <small class="text-muted">${product.code}</small><br>
                            <strong>${formatRupiah(product.selling_price)}</strong><br>
                            Stok: ${product.stock_quantity} ${product.unit}
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            ${stockBadge}
                            <button class="btn ${buttonClass} btn-sm add-to-cart"
                                    data-product-id="${product.id}" ${buttonDisabled}>
                                <i class="fas fa-plus"></i> ${buttonText}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        grid.append(productCard);
    });

    // Add to cart event
    $('.add-to-cart').on('click', function() {
        const productId = $(this).data('product-id');
        addToCart(productId);
    });
}

/**
 * Add product to cart
 */
function addToCart(productId) {
    const product = products.find(p => p.id == productId);
    if (!product) return;

    if (product.stock_quantity === 0) {
        showError('Produk tidak tersedia');
        return;
    }

    // Check if product already in cart
    const existingItem = cart.find(item => item.product_id == productId);

    if (existingItem) {
        if (existingItem.quantity >= product.stock_quantity) {
            showError('Quantity melebihi stok tersedia');
            return;
        }
        existingItem.quantity++;
        existingItem.final_price = existingItem.price - existingItem.discount_amount;
        existingItem.total = existingItem.quantity * existingItem.final_price;
    } else {
        cart.push({
            product_id: parseInt(productId),
            code: product.code,
            name: product.name,
            price: parseFloat(product.selling_price),
            cost: parseFloat(product.purchase_price),
            quantity: 1,
            discount_amount: 0,
            final_price: parseFloat(product.selling_price),
            total: parseFloat(product.selling_price),
            max_stock: parseInt(product.stock_quantity)
        });
    }

    renderCart();
    updateCartSummary();
}

/**
 * Remove item from cart
 */
function removeFromCart(productId) {
    cart = cart.filter(item => item.product_id != productId);
    renderCart();
    updateCartSummary();
}

/**
 * Update cart item quantity
 */
function updateCartQuantity(productId, quantity) {
    const item = cart.find(item => item.product_id == productId);
    if (!item) return;

    const newQuantity = parseInt(quantity);

    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }

    if (newQuantity > item.max_stock) {
        showError('Quantity melebihi stok tersedia');
        return;
    }

    item.quantity = newQuantity;
    item.final_price = item.price - item.discount_amount;
    item.total = newQuantity * parseFloat(item.final_price);

    // Update display without full re-render
    const row = $(`.qty-input[data-product-id="${productId}"]`).closest('tr');
    row.find('td:eq(4) small').text(formatRupiah(item.final_price)); // Harga Final
    row.find('td:eq(5) small').text(formatRupiah(item.total)); // Total

    updateCartSummary();
}

/**
 * Update item discount silently (without re-render) - for real-time input
 */
function updateItemDiscountSilent(productId, discountAmount) {
    const item = cart.find(item => item.product_id == productId);
    if (!item) return;

    const discount = parseFloat(discountAmount) || 0;

    // Validasi: diskon tidak boleh lebih dari harga asli
    if (discount > item.price) {
        return; // Don't update if invalid, wait for change event
    }

    // Update item data
    item.discount_amount = discount;
    item.final_price = item.price - discount;
    item.total = item.quantity * item.final_price;

    // Update display without re-rendering entire cart
    const row = $(`.discount-input[data-product-id="${productId}"]`).closest('tr');
    row.find('td:eq(4) small').text(formatRupiah(item.final_price)); // Harga Final
    row.find('td:eq(5) small').text(formatRupiah(item.total)); // Total

    // Update cart summary
    updateCartSummary();
}

/**
 * Update item discount with validation and re-render
 */
function updateItemDiscount(productId, discountAmount) {
    const item = cart.find(item => item.product_id == productId);
    if (!item) return;

    const discount = parseFloat(discountAmount) || 0;

    // Validasi: diskon tidak boleh lebih dari harga asli
    if (discount > item.price) {
        showError('Diskon tidak boleh lebih dari harga produk');
        // Reset discount input
        $(`.discount-input[data-product-id="${productId}"]`).val(formatNumber(item.discount_amount));
        return;
    }

    // Update item
    item.discount_amount = discount;
    item.final_price = item.price - discount;
    item.total = item.quantity * item.final_price;

    // Update display without full re-render
    const row = $(`.discount-input[data-product-id="${productId}"]`).closest('tr');
    row.find('td:eq(4) small').text(formatRupiah(item.final_price)); // Harga Final
    row.find('td:eq(5) small').text(formatRupiah(item.total)); // Total

    // Update cart summary
    updateCartSummary();
}

/**
 * Render cart items
 */
function renderCart() {
    const cartItems = $('#cartItems');
    cartItems.empty();

    if (cart.length === 0) {
        cartItems.html(`
            <tr id="emptyCart">
                <td colspan="7" class="text-center text-muted py-3">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <p>Keranjang kosong</p>
                </td>
            </tr>
        `);
        return;
    }

    cart.forEach(function(item) {
        const row = `
            <tr>
                <td>
                    <small><strong>${item.name}</strong><br>
                    ${item.code}</small>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm text-center qty-input"
                           value="${item.quantity}" min="1" max="${item.max_stock}"
                           data-product-id="${item.product_id}" style="width: 70px;">
                </td>
                <td><small>${formatRupiah(item.price)}</small></td>
                <td>
                    <input type="text" class="form-control form-control-sm text-center currency-input discount-input"
                           value="${formatNumber(item.discount_amount)}"
                           data-product-id="${item.product_id}" style="width: 80px;"
                           placeholder="0">
                </td>
                <td><small>${formatRupiah(item.final_price)}</small></td>
                <td><small>${formatRupiah(item.total)}</small></td>
                <td>
                    <button class="btn btn-outline-danger btn-sm remove-item"
                            data-product-id="${item.product_id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        cartItems.append(row);
    });

    // Cart events - using event delegation to avoid re-binding
    // (Event handlers will be set up once in document ready)
}

/**
 * Update cart summary
 */
function updateCartSummary() {
    const subtotal = cart.reduce((sum, item) => {
        const itemTotal = parseFloat(item.total) || 0;
        return sum + itemTotal;
    }, 0);

    const itemCount = cart.reduce((sum, item) => {
        const itemQty = parseInt(item.quantity) || 0;
        return sum + itemQty;
    }, 0);

    // Debug log
    console.log('Cart:', cart);
    console.log('Subtotal:', subtotal);
    console.log('Item Count:', itemCount);

    $('#cartSubtotal').text(formatRupiah(subtotal));
    $('#cartTotal').text(formatRupiah(subtotal));
    $('#cartItemCount').text(itemCount);

    // Enable/disable payment button
    $('#processPayment').prop('disabled', cart.length === 0);
}

/**
 * Clear cart
 */
function clearCart() {
    cart = [];
    renderCart();
    updateCartSummary();
    showSuccess('Keranjang berhasil dikosongkan');
}

/**
 * Add quick customer
 */
function addQuickCustomer() {
    const formData = new FormData($('#quickCustomerForm')[0]);

    // Generate customer code
    const code = 'CUST' + Math.floor(Math.random() * 9000 + 1000);
    formData.append('code', code);
    formData.append('is_active', 1);

    $.ajax({
        url: '../api/customer_save.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // Add to customer dropdown
                const option = `<option value="${response.data.id}" selected>
                    ${formData.get('name')}
                </option>`;
                $('#customerId').append(option);

                $('#quickCustomerModal').modal('hide');
                $('#quickCustomerForm')[0].reset();
                showSuccess('Pelanggan berhasil ditambahkan');
            } else {
                showError(response.message);
            }
        },
        error: function() {
            showError('Gagal menambahkan pelanggan');
        }
    });
}

/**
 * Handle payment type change
 */
function handlePaymentTypeChange(paymentType) {
    const total = cart.reduce((sum, item) => sum + item.total, 0);

    if (paymentType === 'full') {
        // Bayar Lunas
        $('#paymentHint').text('Masukkan jumlah pembayaran');
        $('#paymentAmount').val(formatNumber(total));
        $('#changeSection').show();
        $('#remainingSection').hide();
    } else {
        // Bayar DP
        $('#paymentHint').text('Masukkan jumlah DP (uang muka)');
        $('#paymentAmount').val('');
        $('#changeSection').hide();
        $('#remainingSection').show();
    }

    calculateChange();
}

/**
 * Show payment modal
 */
function showPaymentModal() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);

    // Build payment summary
    let summaryHtml = `
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Produk</th>
                    <th>Qty</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
    `;

    cart.forEach(function(item) {
        summaryHtml += `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${formatRupiah(item.total)}</td>
            </tr>
        `;
    });

    summaryHtml += `
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="2">Total</th>
                    <th>${formatRupiah(subtotal)}</th>
                </tr>
            </tfoot>
        </table>
    `;

    $('#paymentSummary').html(summaryHtml);

    // Reset payment type to full
    $('#paymentTypeFull').prop('checked', true);
    handlePaymentTypeChange('full');

    $('#paymentModal').modal('show');
}

/**
 * Calculate payment change
 */
function calculateChange() {
    const total = cart.reduce((sum, item) => {
        const itemTotal = parseFloat(item.total) || 0;
        return sum + itemTotal;
    }, 0);

    const payment = parseNumber($('#paymentAmount').val());
    const paymentType = $('input[name="paymentType"]:checked').val();

    if (paymentType === 'full') {
        // Bayar Lunas
        const change = payment - total;
        $('#paymentChange').val(change >= 0 ? formatRupiah(change) : 'Kurang bayar');

        // Enable/disable complete button
        $('#completeSale').prop('disabled', change < 0);
    } else {
        // Bayar DP
        const remaining = total - payment;
        $('#paymentRemaining').val(remaining > 0 ? formatRupiah(remaining) : formatRupiah(0));

        // Enable/disable complete button - DP bisa 0 atau lebih
        $('#completeSale').prop('disabled', payment < 0);
    }
}

/**
 * Complete sale transaction
 */
function completeSale() {
    const customerId = $('#customerId').val();
    const customerName = $('#customerId option:selected').text();
    const paymentAmount = parseNumber($('#paymentAmount').val());
    const paymentType = $('input[name="paymentType"]:checked').val();
    const notes = $('#paymentNotes').val();

    const totalAmount = cart.reduce((sum, item) => sum + item.total, 0);

    const saleData = {
        customer_id: customerId || null,
        customer_name: customerName || 'Walk-in',
        items: cart,
        payment_amount: paymentAmount,
        payment_type: paymentType,
        total_amount: totalAmount,
        notes: notes
    };

    showLoading('Memproses transaksi...');

    $.ajax({
        url: '../api/pos_complete_sale.php',
        type: 'POST',
        data: JSON.stringify(saleData),
        contentType: 'application/json',
        success: function(response) {
            hideLoading();

            if (response.success) {
                const data = response.data;

                // Show appropriate success message
                let successMessage = response.message;
                if (data.payment_status === 'partial') {
                    successMessage += `<br><strong>Sisa pembayaran: ${formatRupiah(data.remaining_amount)}</strong>`;
                }

                showSuccess(successMessage);

                // Ask to print receipt
                Swal.fire({
                    title: data.payment_status === 'partial' ? 'Transaksi DP Berhasil!' : 'Transaksi Berhasil!',
                    html: data.payment_status === 'partial'
                        ? `Invoice: ${data.invoice_number}<br>Sisa bayar: ${formatRupiah(data.remaining_amount)}<br><br>Apakah ingin mencetak struk?`
                        : 'Apakah ingin mencetak struk?',
                    icon: 'success',
                    showCancelButton: true,
                    confirmButtonText: 'Cetak Struk',
                    cancelButtonText: 'Tidak'
                }).then((result) => {
                    if (result.isConfirmed) {
                        printReceipt(response.data.sale_id);
                    }
                });

                // Reset form
                clearCart();
                $('#customerId').val('');
                $('#paymentModal').modal('hide');
                $('#paymentNotes').val('');
                $('#paymentTypeFull').prop('checked', true);
                handlePaymentTypeChange('full');

            } else {
                showError(response.message);
            }
        },
        error: function() {
            hideLoading();
            showError('Gagal memproses transaksi');
        }
    });
}

/**
 * Print receipt
 */
function printReceipt(saleId) {
    window.open(`../api/print_receipt.php?id=${saleId}`, '_blank');
}
