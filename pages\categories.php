<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Data Kategori';
$currentPage = 'categories';
$pageIcon = 'fas fa-tags';
$breadcrumb = [
    ['title' => 'Master Data', 'url' => '#'],
    ['title' => 'Data Kategori']
];

include '../includes/header.php';
?>

<div class="card shadow">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-white">
            <i class="fas fa-tags me-2"></i>
            Data Kategori
        </h6>
        <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#categoryModal">
            <i class="fas fa-plus me-1"></i>
            <PERSON>bah Kategori
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="categoriesTable" class="table table-striped table-hover" style="width:100%">
                <thead>
                    <tr>
                        <th>Nama Kategori</th>
                        <th>Deskripsi</th>
                        <th>Jumlah Produk</th>
                        <th>Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Tambah Kategori</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="categoryId" name="id">

                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Nama Kategori *</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalJS = '
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $("#categoriesTable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "../api/categories.php",
            type: "POST"
        },
        columns: [
            { data: 0, width: "25%" },
            { data: 1, width: "35%" },
            { data: 2, width: "15%" },
            { data: 3, width: "15%" },
            { data: 4, width: "10%", orderable: false }
        ],
        order: [[0, "asc"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true
    });

    // Add Category
    $("#categoryForm").on("submit", function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var isEdit = $("#categoryId").val() !== "";

        $.ajax({
            url: "../api/category_save.php",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $("#categoryModal").modal("hide");
                    table.ajax.reload();
                    showSuccess(response.message);
                } else {
                    showError(response.message);
                }
            },
            error: function() {
                showError("Terjadi kesalahan saat menyimpan data");
            }
        });
    });

    // Edit Category
    $(document).on("click", ".btn-edit", function() {
        var id = $(this).data("id");

        $.ajax({
            url: "../api/category_get.php",
            type: "GET",
            data: { id: id },
            success: function(response) {
                if (response.success) {
                    var category = response.data;

                    $("#categoryId").val(category.id);
                    $("#categoryName").val(category.name);
                    $("#categoryDescription").val(category.description);

                    $("#categoryModalLabel").text("Edit Kategori");
                    $("#categoryModal").modal("show");
                } else {
                    showError(response.message);
                }
            }
        });
    });

    // Delete Category
    $(document).on("click", ".btn-delete", function() {
        var id = $(this).data("id");

        confirmDelete(function() {
            $.ajax({
                url: "../api/category_delete.php",
                type: "POST",
                data: { id: id },
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload();
                        showSuccess(response.message);
                    } else {
                        showError(response.message);
                    }
                }
            });
        });
    });

    // Reset form when modal is hidden
    $("#categoryModal").on("hidden.bs.modal", function() {
        $("#categoryForm")[0].reset();
        $("#categoryId").val("");
        $("#categoryModalLabel").text("Tambah Kategori");
    });
});
</script>
';

include '../includes/footer.php';
?>
