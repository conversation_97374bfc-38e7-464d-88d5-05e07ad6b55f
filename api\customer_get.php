<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID pelanggan diperlukan');
    }
    
    $customer = $db->fetchOne("SELECT * FROM customers WHERE id = ?", [$id]);
    
    if (!$customer) {
        throw new Exception('Pelanggan tidak ditemukan');
    }
    
    jsonResponse(true, 'Data pelanggan berhasil diambil', $customer);
    
} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
