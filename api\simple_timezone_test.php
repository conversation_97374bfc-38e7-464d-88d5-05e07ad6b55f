<?php
// Simple timezone test for Riwayat Penjualan

echo "<h2>🕐 Simple Timezone Test</h2>";

echo "<h3>1. PHP Timezone Test:</h3>";
echo "<p><strong>Before setting:</strong> " . date_default_timezone_get() . " - " . date('Y-m-d H:i:s T') . "</p>";

// Set Makassar timezone
date_default_timezone_set('Asia/Makassar');
echo "<p><strong>After setting Makassar:</strong> " . date_default_timezone_get() . " - " . date('Y-m-d H:i:s T') . "</p>";

echo "<h3>2. API Files Check:</h3>";

$apiFiles = [
    'sales.php',
    'sale_detail.php',
    'sale_delete.php',
    'print_receipt.php'
];

echo "<table border='1' cellpadding='8' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>File</th><th>Timezone Status</th></tr>";

foreach ($apiFiles as $file) {
    $filePath = __DIR__ . '/' . $file;
    $status = '❌ Not found';

    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        if (strpos($content, "date_default_timezone_set('Asia/Makassar')") !== false) {
            $status = '✅ Timezone set';
        } else {
            $status = '⚠️ No timezone';
        }
    }

    echo "<tr><td><strong>$file</strong></td><td>$status</td></tr>";
}

echo "</table>";

echo "<h3>3. Functions Test:</h3>";

try {
    require_once '../includes/functions.php';

    $testDate = '2024-12-26';
    $testDateTime = '2024-12-26 14:30:45';

    echo "<table border='1' cellpadding='8' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Function</th><th>Input</th><th>Output</th></tr>";

    echo "<tr>";
    echo "<td><strong>formatDate()</strong></td>";
    echo "<td>$testDate</td>";
    echo "<td>" . formatDate($testDate) . "</td>";
    echo "</tr>";

    echo "<tr>";
    echo "<td><strong>formatDateTime()</strong></td>";
    echo "<td>$testDateTime</td>";
    echo "<td>" . formatDateTime($testDateTime) . "</td>";
    echo "</tr>";

    echo "<tr>";
    echo "<td><strong>date() current</strong></td>";
    echo "<td>now</td>";
    echo "<td>" . date('d/m/Y H:i:s') . "</td>";
    echo "</tr>";

    echo "</table>";

} catch (Exception $e) {
    echo "<p style='color: red;'>Functions Error: " . $e->getMessage() . "</p>";
}

echo "<h3>4. Database Connection Test:</h3>";

try {
    require_once '../config/database.php';

    // Use global database instance
    global $db;
    echo "<p>✅ Database connection successful</p>";

    // Simple query test
    $result = $db->query("SELECT 1 as test");
    echo "<p>✅ Database query test successful</p>";

    // Check if sales table exists
    try {
        $salesCount = $db->fetchOne("SELECT COUNT(*) as count FROM sales");
        echo "<p>✅ Sales table accessible - " . $salesCount['count'] . " records found</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Sales table issue: " . $e->getMessage() . "</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}

echo "<h3>5. Timezone Comparison:</h3>";

$timezones = [
    'UTC' => 'UTC (GMT+0)',
    'Asia/Jakarta' => 'Jakarta (WIB - GMT+7)',
    'Asia/Makassar' => 'Makassar (WITA - GMT+8)',
    'Asia/Jayapura' => 'Jayapura (WIT - GMT+9)'
];

echo "<table border='1' cellpadding='8' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>Location</th><th>Current Time</th></tr>";

foreach ($timezones as $tz => $description) {
    $originalTz = date_default_timezone_get();
    date_default_timezone_set($tz);
    $time = date('Y-m-d H:i:s');

    $rowStyle = $tz === 'Asia/Makassar' ? " style='background: #d4edda; font-weight: bold;'" : "";
    echo "<tr$rowStyle>";
    echo "<td>$description</td>";
    echo "<td>$time</td>";
    echo "</tr>";

    date_default_timezone_set($originalTz);
}

echo "</table>";

echo "<h3>✅ Test Summary:</h3>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
echo "<ul style='color: #155724;'>";
echo "<li>✅ Current timezone: <strong>" . date_default_timezone_get() . "</strong></li>";
echo "<li>✅ Current time: <strong>" . date('d/m/Y H:i:s T') . "</strong></li>";
echo "<li>✅ Indonesian date format: <strong>" . date('d F Y') . "</strong></li>";
echo "<li>✅ Time with timezone: <strong>" . date('H:i:s T') . "</strong></li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin-top: 20px;'>";
echo "<a href='../pages/sales.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Riwayat Penjualan</a>";
echo "<a href='test_timezone.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Full Timezone Test</a>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}
h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
table {
    margin: 10px 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
th {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}
td, th {
    padding: 8px;
    text-align: left;
}
p {
    margin: 10px 0;
}
ul {
    margin: 10px 0;
}
</style>
