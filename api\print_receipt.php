<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $id = $_GET['id'] ?? null;

    if (!$id) {
        throw new Exception('ID transaksi diperlukan');
    }

    // Get sale data
    $sale = $db->fetchOne("
        SELECT
            s.*,
            c.name as customer_db_name,
            c.phone as customer_phone,
            c.address as customer_address
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        WHERE s.id = ?
    ", [$id]);

    if (!$sale) {
        throw new Exception('Transaksi tidak ditemukan');
    }

    // Get sale items
    $items = $db->fetchAll("
        SELECT
            si.*,
            p.unit
        FROM sale_items si
        LEFT JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ", [$id]);

    $customerName = $sale['customer_db_name'] ?: $sale['customer_name'];

} catch (Exception $e) {
    die('Error: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk UD. Nadia Mebel - <?php echo $sale['invoice_number']; ?></title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .receipt {
            width: 300px;
            margin: 0 auto;
            background: white;
            padding: 10px;
        }
        .header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .transaction-info {
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        .items-table {
            width: 100%;
            margin-bottom: 10px;
        }
        .item-row {
            margin-bottom: 5px;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 3px;
        }
        .item-name {
            font-weight: bold;
        }
        .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }
        .totals {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 15px;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 10px;
        }
        @media print {
            body { margin: 0; padding: 0; }
            .receipt { width: 100%; margin: 0; padding: 5px; }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- Header -->
        <div class="header">
            <div class="company-name">UD. NADIA MEBEL</div>
            <div class="company-info">Jl. Bojonegoro Blok B RT. 13 Simpang Pasir Palaran</div>
            <div class="company-info">HP: 0857-8727-5318</div>
        </div>

        <!-- Transaction Info -->
        <div class="transaction-info">
            <div class="info-row">
                <span>Invoice:</span>
                <span><?php echo $sale['invoice_number']; ?></span>
            </div>
            <div class="info-row">
                <span>Tanggal:</span>
                <span><?php echo formatDate($sale['sale_date']); ?></span>
            </div>
            <div class="info-row">
                <span>Waktu:</span>
                <span><?php echo date('H:i:s', strtotime($sale['created_at'])); ?></span>
            </div>
            <div class="info-row">
                <span>Pelanggan:</span>
                <span><?php echo $customerName; ?></span>
            </div>
            <?php if (($sale['payment_status'] ?? 'paid') === 'partial'): ?>
            <div class="info-row">
                <span>Status:</span>
                <span>DP (Belum Lunas)</span>
            </div>
            <?php endif; ?>
        </div>

        <!-- Items -->
        <div class="items-table">
            <?php foreach ($items as $item): ?>
            <div class="item-row">
                <div class="item-name"><?php echo $item['product_name']; ?></div>
                <div class="item-details">
                    <span><?php echo $item['quantity']; ?> x <?php echo formatRupiah($item['unit_price']); ?></span>
                    <span><?php echo formatRupiah($item['total_price']); ?></span>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Totals -->
        <div class="totals">
            <div class="total-row">
                <span>Subtotal:</span>
                <span><?php echo formatRupiah($sale['total_amount']); ?></span>
            </div>
            <div class="total-row grand-total">
                <span>TOTAL:</span>
                <span><?php echo formatRupiah($sale['total_amount']); ?></span>
            </div>

            <?php if (($sale['payment_status'] ?? 'paid') === 'partial'): ?>
            <div style="margin-top: 10px; border-top: 1px dashed #000; padding-top: 5px;">
                <div class="total-row">
                    <span>Dibayar (DP):</span>
                    <span><?php echo formatRupiah($sale['paid_amount'] ?? 0); ?></span>
                </div>
                <div class="total-row" style="font-weight: bold; color: red;">
                    <span>Sisa Bayar:</span>
                    <span><?php echo formatRupiah($sale['remaining_amount'] ?? 0); ?></span>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($sale['notes'])): ?>
        <div style="margin-top: 10px; border-top: 1px dashed #000; padding-top: 10px;">
            <strong>Catatan:</strong><br>
            <?php echo nl2br(htmlspecialchars($sale['notes'])); ?>
        </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="footer">
            <div>*** TERIMA KASIH ***</div>
            <div>Barang yang sudah dibeli tidak dapat dikembalikan</div>
            <div>Simpan struk ini sebagai bukti pembelian</div>
            <div style="margin-top: 10px;">
                Dicetak: <?php echo date('d/m/Y H:i:s'); ?>
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }

        // Close window after printing
        window.onafterprint = function() {
            window.close();
        }
    </script>
</body>
</html>
