<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    // DataTables parameters
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $searchValue = $_POST['search']['value'];

    // Date filter
    $dateFrom = $_POST['date_from'] ?? '';
    $dateTo = $_POST['date_to'] ?? '';

    // Column mapping for ordering
    $columns = [
        0 => 'invoice_number',
        1 => 'sale_date',
        2 => 'customer_name',
        3 => 'total_amount',
        4 => 'profit',
        5 => 'created_at'
    ];

    // Base query
    $baseQuery = "
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
    ";

    // Search and filter conditions
    $whereConditions = [];
    $params = [];

    if (!empty($searchValue)) {
        $whereConditions[] = "(
            s.invoice_number LIKE ? OR
            s.customer_name LIKE ? OR
            c.name LIKE ? OR
            s.notes LIKE ?
        )";
        $searchTerm = "%$searchValue%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }

    if (!empty($dateFrom)) {
        $whereConditions[] = "s.sale_date >= ?";
        $params[] = $dateFrom;
    }

    if (!empty($dateTo)) {
        $whereConditions[] = "s.sale_date <= ?";
        $params[] = $dateTo;
    }

    $whereClause = !empty($whereConditions) ? " WHERE " . implode(" AND ", $whereConditions) : "";

    // Count total records
    $totalRecords = $db->fetchOne("SELECT COUNT(*) as total " . $baseQuery)['total'];

    // Count filtered records
    $filteredRecords = $db->fetchOne(
        "SELECT COUNT(*) as total " . $baseQuery . $whereClause,
        $params
    )['total'];

    // Order condition
    $orderColumn = $columns[$_POST['order'][0]['column']] ?? 's.created_at';
    $orderDirection = $_POST['order'][0]['dir'] === 'desc' ? 'DESC' : 'ASC';
    $orderCondition = " ORDER BY $orderColumn $orderDirection";

    // Limit condition
    $limitCondition = " LIMIT $start, $length";

    // Main query
    $query = "
        SELECT
            s.id,
            s.invoice_number,
            s.customer_id,
            s.customer_name,
            s.sale_date,
            s.total_amount,
            s.total_cost,
            s.profit,
            s.payment_status,
            s.paid_amount,
            s.remaining_amount,
            s.notes,
            s.created_at,
            c.name as customer_db_name,
            (SELECT COUNT(*) FROM sale_items WHERE sale_id = s.id) as item_count,
            (SELECT SUM(discount_amount) FROM sale_items WHERE sale_id = s.id) as total_discount
        " . $baseQuery . $whereClause . $orderCondition . $limitCondition;

    $sales = $db->fetchAll($query, $params);

    // Format data for DataTables
    $data = [];
    foreach ($sales as $sale) {
        $customerName = $sale['customer_db_name'] ?: $sale['customer_name'];
        $profitPercentage = $sale['total_cost'] > 0
            ? round(($sale['profit'] / $sale['total_cost']) * 100, 1)
            : 0;

        // Payment status badge
        $paymentStatus = $sale['payment_status'] ?? 'paid';
        $statusBadge = '';
        if ($paymentStatus === 'partial') {
            $statusBadge = '<span class="badge bg-warning text-dark ms-1">DP</span>';
        } elseif ($paymentStatus === 'paid') {
            $statusBadge = '<span class="badge bg-success ms-1">Lunas</span>';
        }

        // Discount info
        $totalDiscount = floatval($sale['total_discount'] ?? 0);
        $discountInfo = $totalDiscount > 0 ? '<br><small class="text-info">Diskon: ' . formatRupiah($totalDiscount) . '</small>' : '';

        // Payment info for DP transactions
        $paymentInfo = '';
        if ($paymentStatus === 'partial') {
            $paidAmount = floatval($sale['paid_amount'] ?? 0);
            $remainingAmount = floatval($sale['remaining_amount'] ?? 0);
            $paymentInfo = '<br><small class="text-muted">Dibayar: ' . formatRupiah($paidAmount) . '</small>';
            $paymentInfo .= '<br><small class="text-danger">Sisa: ' . formatRupiah($remainingAmount) . '</small>';
        }

        // Action buttons
        $actions = '
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-info btn-view"
                        data-id="' . $sale['id'] . '"
                        title="Detail">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" class="btn btn-outline-success btn-print"
                        data-id="' . $sale['id'] . '"
                        title="Cetak">
                    <i class="fas fa-print"></i>
                </button>';

        // Add payment button for partial payments
        if ($paymentStatus === 'partial') {
            $actions .= '
                <button type="button" class="btn btn-outline-warning btn-payment"
                        data-id="' . $sale['id'] . '"
                        title="Bayar Cicilan">
                    <i class="fas fa-money-bill-wave"></i>
                </button>';
        }

        $actions .= '
                <button type="button" class="btn btn-outline-danger btn-delete"
                        data-id="' . $sale['id'] . '"
                        title="Hapus">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        ';

        $data[] = [
            '<strong>' . htmlspecialchars($sale['invoice_number']) . '</strong>' . $statusBadge,
            formatDate($sale['sale_date']),
            '<div>
                <strong>' . htmlspecialchars($customerName) . '</strong><br>
                <small class="text-muted">' . $sale['item_count'] . ' item</small>
            </div>',
            '<div class="text-end">' . formatRupiah($sale['total_amount']) . $discountInfo . $paymentInfo . '</div>',
            '<div class="text-end">
                ' . formatRupiah($sale['profit']) . '<br>
                <small class="text-success">+' . $profitPercentage . '%</small>
            </div>',
            formatDateTime($sale['created_at']),
            '<div class="text-center">' . $actions . '</div>'
        ];
    }

    // Response
    $response = [
        'draw' => $draw,
        'recordsTotal' => $totalRecords,
        'recordsFiltered' => $filteredRecords,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'draw' => $_POST['draw'] ?? 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ]);
}
?>
