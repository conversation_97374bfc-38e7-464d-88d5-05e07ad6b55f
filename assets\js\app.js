/**
 * Custom JavaScript for POS Application
 */

$(document).ready(function() {

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Sidebar navigation highlighting
    highlightCurrentPage();

    // Auto-collapse mobile sidebar after navigation
    $('.sidebar .nav-link').on('click', function() {
        if (window.innerWidth < 768) {
            $('#sidebarMenu').collapse('hide');
        }
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Prevent double form submission
    $('form').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);

        // Re-enable after 3 seconds to prevent permanent disable
        setTimeout(function() {
            submitBtn.prop('disabled', false);
        }, 3000);
    });

    // Format currency inputs
    $(document).on('input', '.currency-input', function() {
        var value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = formatNumber(parseInt(value));
        } else {
            this.value = '';
        }
    });

    // Format number inputs
    $(document).on('input', '.number-input', function() {
        var value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = formatNumber(parseInt(value));
        } else {
            this.value = '';
        }
    });

    // Auto-generate product code
    $('#categoryId').on('change', function() {
        var categoryId = $(this).val();
        if (categoryId && !$('#productId').val()) {
            generateProductCode(categoryId);
        }
    });

    // Calculate profit when prices change
    $('#purchasePrice, #sellingPrice').on('input', function() {
        calculateProfit();
    });

    // Confirm before leaving page with unsaved changes
    var formChanged = false;
    $('form input, form select, form textarea').on('change', function() {
        formChanged = true;
    });

    $('form').on('submit', function() {
        formChanged = false;
    });

    $(window).on('beforeunload', function() {
        if (formChanged) {
            return 'Anda memiliki perubahan yang belum disimpan. Yakin ingin meninggalkan halaman?';
        }
    });
});

// Global utility functions

/**
 * Format number with thousand separators
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}

/**
 * Format currency to Rupiah
 */
function formatRupiah(amount) {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Parse formatted number back to integer
 */
function parseNumber(formattedNumber) {
    return parseInt(formattedNumber.replace(/[^\d]/g, '')) || 0;
}

/**
 * Show loading overlay
 */
function showLoading(message = 'Loading...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

/**
 * Hide loading overlay
 */
function hideLoading() {
    Swal.close();
}

/**
 * Show success message
 */
function showSuccess(message, timer = 2000) {
    Swal.fire({
        icon: 'success',
        title: 'Berhasil!',
        text: message,
        timer: timer,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
    });
}

/**
 * Show error message
 */
function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: message,
        confirmButtonText: 'OK'
    });
}

/**
 * Show warning message
 */
function showWarning(message) {
    Swal.fire({
        icon: 'warning',
        title: 'Peringatan!',
        text: message,
        confirmButtonText: 'OK'
    });
}

/**
 * Show info message
 */
function showInfo(message) {
    Swal.fire({
        icon: 'info',
        title: 'Informasi',
        text: message,
        confirmButtonText: 'OK'
    });
}

/**
 * Confirm delete action
 */
function confirmDelete(callback, message = "Data yang dihapus tidak dapat dikembalikan!") {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            callback();
        }
    });
}

/**
 * Confirm action
 */
function confirmAction(title, text, callback) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            callback();
        }
    });
}

/**
 * Generate product code based on category
 */
function generateProductCode(categoryId) {
    $.ajax({
        url: '../api/generate_product_code.php',
        type: 'GET',
        data: { category_id: categoryId },
        success: function(response) {
            if (response.success) {
                $('#productCode').val(response.data.code);
            }
        }
    });
}

/**
 * Calculate profit percentage
 */
function calculateProfit() {
    var purchasePrice = parseNumber($('#purchasePrice').val());
    var sellingPrice = parseNumber($('#sellingPrice').val());

    if (purchasePrice > 0 && sellingPrice > 0) {
        var profit = sellingPrice - purchasePrice;
        var profitPercentage = (profit / purchasePrice) * 100;

        var profitInfo = '';
        if (profit > 0) {
            profitInfo = '<small class="text-success">Laba: ' + formatRupiah(profit) + ' (' + profitPercentage.toFixed(1) + '%)</small>';
        } else if (profit < 0) {
            profitInfo = '<small class="text-danger">Rugi: ' + formatRupiah(Math.abs(profit)) + ' (' + Math.abs(profitPercentage).toFixed(1) + '%)</small>';
        } else {
            profitInfo = '<small class="text-muted">Tidak ada laba/rugi</small>';
        }

        $('#profitInfo').html(profitInfo);
    } else {
        $('#profitInfo').html('');
    }
}

/**
 * Validate form before submit
 */
function validateForm(formId) {
    var form = $(formId)[0];
    if (form.checkValidity() === false) {
        form.classList.add('was-validated');
        return false;
    }
    return true;
}

/**
 * Reset form validation
 */
function resetFormValidation(formId) {
    $(formId)[0].classList.remove('was-validated');
}

/**
 * Debounce function for search inputs
 */
function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * Print element
 */
function printElement(elementId) {
    var printContents = document.getElementById(elementId).innerHTML;
    var originalContents = document.body.innerHTML;

    document.body.innerHTML = printContents;
    window.print();
    document.body.innerHTML = originalContents;
    location.reload();
}

/**
 * Export table to CSV
 */
function exportTableToCSV(tableId, filename) {
    var csv = [];
    var rows = document.querySelectorAll('#' + tableId + ' tr');

    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll('td, th');

        for (var j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }

        csv.push(row.join(','));
    }

    downloadCSV(csv.join('\n'), filename);
}

/**
 * Download CSV file
 */
function downloadCSV(csv, filename) {
    var csvFile = new Blob([csv], { type: 'text/csv' });
    var downloadLink = document.createElement('a');

    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

/**
 * Highlight current page in sidebar
 */
function highlightCurrentPage() {
    var currentPath = window.location.pathname;
    var currentFile = currentPath.split('/').pop();

    // Remove active class from all nav links
    $('.sidebar .nav-link').removeClass('active');

    // Add active class to current page
    $('.sidebar .nav-link').each(function() {
        var href = $(this).attr('href');
        var linkFile = href.split('/').pop();

        if (linkFile === currentFile ||
            (currentFile === '' && linkFile === 'index.php') ||
            (currentFile === 'index.php' && linkFile === 'index.php')) {
            $(this).addClass('active');
        }
    });
}

/**
 * Navigate to page
 */
function navigateTo(url) {
    window.location.href = url;
}

/**
 * Refresh current page
 */
function refreshPage() {
    window.location.reload();
}
