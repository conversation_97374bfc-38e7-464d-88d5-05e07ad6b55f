<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    // DataTables parameters
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $searchValue = $_POST['search']['value'];
    
    // Column mapping for ordering
    $columns = [
        0 => 'name',
        1 => 'description',
        2 => 'created_at'
    ];
    
    // Base query
    $baseQuery = "FROM categories";
    
    // Search condition
    $searchCondition = "";
    $searchParams = [];
    if (!empty($searchValue)) {
        $searchCondition = " WHERE (name LIKE ? OR description LIKE ?)";
        $searchTerm = "%$searchValue%";
        $searchParams = [$searchTerm, $searchTerm];
    }
    
    // Count total records
    $totalRecords = $db->fetchOne("SELECT COUNT(*) as total " . $baseQuery)['total'];
    
    // Count filtered records
    if (!empty($searchValue)) {
        $filteredRecords = $db->fetchOne(
            "SELECT COUNT(*) as total " . $baseQuery . $searchCondition,
            $searchParams
        )['total'];
    } else {
        $filteredRecords = $totalRecords;
    }
    
    // Order condition
    $orderColumn = $columns[$_POST['order'][0]['column']] ?? 'name';
    $orderDirection = $_POST['order'][0]['dir'] === 'desc' ? 'DESC' : 'ASC';
    $orderCondition = " ORDER BY $orderColumn $orderDirection";
    
    // Limit condition
    $limitCondition = " LIMIT $start, $length";
    
    // Main query
    $query = "
        SELECT 
            id,
            name,
            description,
            created_at,
            (SELECT COUNT(*) FROM products WHERE category_id = categories.id) as product_count
        " . $baseQuery . $searchCondition . $orderCondition . $limitCondition;
    
    $categories = $db->fetchAll($query, $searchParams);
    
    // Format data for DataTables
    $data = [];
    foreach ($categories as $category) {
        // Action buttons
        $actions = '
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary btn-edit" 
                        data-id="' . $category['id'] . '" 
                        title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-outline-danger btn-delete" 
                        data-id="' . $category['id'] . '" 
                        title="Hapus">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        ';
        
        $data[] = [
            '<strong>' . htmlspecialchars($category['name']) . '</strong>',
            htmlspecialchars($category['description'] ?? ''),
            '<div class="text-center">
                <span class="badge bg-info">' . $category['product_count'] . ' produk</span>
            </div>',
            formatDateTime($category['created_at']),
            '<div class="text-center">' . $actions . '</div>'
        ];
    }
    
    // Response
    $response = [
        'draw' => $draw,
        'recordsTotal' => $totalRecords,
        'recordsFiltered' => $filteredRecords,
        'data' => $data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'draw' => $_POST['draw'] ?? 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ]);
}
?>
