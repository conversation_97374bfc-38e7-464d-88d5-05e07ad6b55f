<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    
    // Build query
    $query = "
        SELECT 
            p.id,
            p.code,
            p.name,
            p.unit,
            p.purchase_price,
            p.selling_price,
            p.stock_quantity,
            p.min_stock,
            c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_active = 1
    ";
    
    $params = [];
    
    // Add search condition
    if (!empty($search)) {
        $query .= " AND (p.code LIKE ? OR p.name LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    // Add category filter
    if (!empty($category)) {
        $query .= " AND p.category_id = ?";
        $params[] = $category;
    }
    
    $query .= " ORDER BY p.name ASC LIMIT 50";
    
    $products = $db->fetchAll($query, $params);
    
    jsonResponse(true, 'Produk berhasil dimuat', $products);
    
} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
