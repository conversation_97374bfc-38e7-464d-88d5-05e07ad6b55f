<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    $db = new Database();
    
    echo "<h2>Create Test DP Data</h2>";
    
    // Check if we have any sales
    $existingSales = $db->fetchAll("SELECT * FROM sales LIMIT 5");
    
    if (count($existingSales) == 0) {
        echo "<p style='color: red;'>No sales data found. Please create some sales transactions first through the POS system.</p>";
        exit;
    }
    
    // Check if we already have partial payments
    $partialSales = $db->fetchAll("SELECT * FROM sales WHERE payment_status = 'partial'");
    
    if (count($partialSales) > 0) {
        echo "<p style='color: green;'>Found " . count($partialSales) . " existing partial payments.</p>";
        foreach ($partialSales as $sale) {
            echo "<p>- {$sale['invoice_number']}: {$sale['customer_name']} - Remaining: " . formatRupiah($sale['remaining_amount']) . "</p>";
        }
    } else {
        echo "<p>No partial payments found. Creating test data...</p>";
        
        // Convert first 3 sales to partial payments
        $salesToConvert = array_slice($existingSales, 0, 3);
        
        foreach ($salesToConvert as $sale) {
            $totalAmount = floatval($sale['total_amount']);
            $dpAmount = $totalAmount * 0.4; // 40% DP
            $remainingAmount = $totalAmount - $dpAmount;
            
            // Update sales record
            $db->update('sales', [
                'payment_status' => 'partial',
                'paid_amount' => $dpAmount,
                'remaining_amount' => $remainingAmount
            ], ['id' => $sale['id']]);
            
            // Insert payment record
            $db->insert('sale_payments', [
                'sale_id' => $sale['id'],
                'payment_date' => date('Y-m-d'),
                'amount' => $dpAmount,
                'payment_type' => 'dp',
                'notes' => 'Test DP data'
            ]);
            
            echo "<p style='color: green;'>✅ Converted {$sale['invoice_number']} to DP:</p>";
            echo "<ul>";
            echo "<li>Customer: {$sale['customer_name']}</li>";
            echo "<li>Total: " . formatRupiah($totalAmount) . "</li>";
            echo "<li>DP Paid: " . formatRupiah($dpAmount) . "</li>";
            echo "<li>Remaining: " . formatRupiah($remainingAmount) . "</li>";
            echo "</ul>";
        }
        
        echo "<p style='color: green;'><strong>Test data created successfully!</strong></p>";
    }
    
    echo "<h3>Current Outstanding DP Summary:</h3>";
    $summary = $db->fetchOne("
        SELECT 
            COUNT(*) as total_customers,
            SUM(total_amount) as total_amount,
            SUM(paid_amount) as total_paid,
            SUM(remaining_amount) as total_outstanding
        FROM sales 
        WHERE payment_status = 'partial'
    ");
    
    echo "<ul>";
    echo "<li>Total Customers: " . $summary['total_customers'] . "</li>";
    echo "<li>Total Outstanding: " . formatRupiah($summary['total_outstanding']) . "</li>";
    echo "<li>Total Paid: " . formatRupiah($summary['total_paid']) . "</li>";
    echo "</ul>";
    
    echo "<p><a href='../pages/outstanding_dp.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Outstanding DP Management</a></p>";
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
ul { margin: 10px 0; }
</style>
