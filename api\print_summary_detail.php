<?php
// Top Products
$topProducts = $db->fetchAll("
    SELECT 
        si.product_name,
        SUM(si.quantity) as total_qty,
        SUM(si.total_price) as total_sales,
        SUM(si.profit) as total_profit
    FROM sale_items si
    JOIN sales s ON si.sale_id = s.id
    WHERE s.sale_date BETWEEN ? AND ?
    GROUP BY si.product_id, si.product_name
    ORDER BY total_sales DESC
    LIMIT 10
", [$dateFrom, $dateTo]);

// Top Customers
$topCustomers = $db->fetchAll("
    SELECT 
        customer_name,
        COUNT(*) as total_transactions,
        SUM(total_amount) as total_purchases
    FROM sales
    WHERE sale_date BETWEEN ? AND ?
    GROUP BY customer_id, customer_name
    ORDER BY total_purchases DESC
    LIMIT 10
", [$dateFrom, $dateTo]);
?>

<!-- Top Products -->
<div class="summary-section">
    <div class="summary-title">PRODUK TERLARIS</div>
    <table class="data-table">
        <thead>
            <tr>
                <th>No</th>
                <th><PERSON>a Produk</th>
                <th>Qty Terjual</th>
                <th>Total Penjualan</th>
                <th>Total Laba</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($topProducts)): ?>
                <?php $no = 1; foreach ($topProducts as $product): ?>
                <tr>
                    <td class="center"><?php echo $no++; ?></td>
                    <td><?php echo htmlspecialchars($product['product_name']); ?></td>
                    <td class="number"><?php echo number_format($product['total_qty']); ?></td>
                    <td class="number"><?php echo formatRupiah($product['total_sales']); ?></td>
                    <td class="number"><?php echo formatRupiah($product['total_profit']); ?></td>
                </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="5" class="center">Tidak ada data produk</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Top Customers -->
<div class="summary-section">
    <div class="summary-title">PELANGGAN TERBAIK</div>
    <table class="data-table">
        <thead>
            <tr>
                <th>No</th>
                <th>Nama Pelanggan</th>
                <th>Total Transaksi</th>
                <th>Total Pembelian</th>
                <th>Rata-rata per Transaksi</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($topCustomers)): ?>
                <?php $no = 1; foreach ($topCustomers as $customer): ?>
                <?php $avgTransaction = $customer['total_transactions'] > 0 ? $customer['total_purchases'] / $customer['total_transactions'] : 0; ?>
                <tr>
                    <td class="center"><?php echo $no++; ?></td>
                    <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                    <td class="number"><?php echo number_format($customer['total_transactions']); ?></td>
                    <td class="number"><?php echo formatRupiah($customer['total_purchases']); ?></td>
                    <td class="number"><?php echo formatRupiah($avgTransaction); ?></td>
                </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="5" class="center">Tidak ada data pelanggan</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
