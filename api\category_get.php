<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }
    
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID kategori diperlukan');
    }
    
    $category = $db->fetchOne("SELECT * FROM categories WHERE id = ?", [$id]);
    
    if (!$category) {
        throw new Exception('Kategori tidak ditemukan');
    }
    
    jsonResponse(true, 'Data kategori berhasil diambil', $category);
    
} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>
