<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validate required fields
    $required = ['code', 'name', 'category_id', 'purchase_price', 'selling_price'];
    foreach ($required as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Field $field is required");
        }
    }
    
    $id = $_POST['id'] ?? null;
    $code = sanitizeInput($_POST['code']);
    $name = sanitizeInput($_POST['name']);
    $description = sanitizeInput($_POST['description'] ?? '');
    $category_id = intval($_POST['category_id']);
    $unit = sanitizeInput($_POST['unit'] ?? 'pcs');
    $purchase_price = floatval($_POST['purchase_price']);
    $selling_price = floatval($_POST['selling_price']);
    $stock_quantity = intval($_POST['stock_quantity'] ?? 0);
    $min_stock = intval($_POST['min_stock'] ?? 0);
    $is_active = intval($_POST['is_active'] ?? 1);
    
    // Validate prices
    if ($purchase_price < 0 || $selling_price < 0) {
        throw new Exception('Harga tidak boleh negatif');
    }
    
    if ($selling_price < $purchase_price) {
        throw new Exception('Harga jual tidak boleh lebih kecil dari harga beli');
    }
    
    // Check if category exists
    $category = $db->fetchOne("SELECT id FROM categories WHERE id = ?", [$category_id]);
    if (!$category) {
        throw new Exception('Kategori tidak ditemukan');
    }
    
    $db->beginTransaction();
    
    if ($id) {
        // Update existing product
        
        // Check if code is unique (excluding current product)
        $existing = $db->fetchOne(
            "SELECT id FROM products WHERE code = ? AND id != ?", 
            [$code, $id]
        );
        if ($existing) {
            throw new Exception('Kode produk sudah digunakan');
        }
        
        // Check if product exists
        $product = $db->fetchOne("SELECT id FROM products WHERE id = ?", [$id]);
        if (!$product) {
            throw new Exception('Produk tidak ditemukan');
        }
        
        $db->update('products', [
            'code' => $code,
            'name' => $name,
            'description' => $description,
            'category_id' => $category_id,
            'unit' => $unit,
            'purchase_price' => $purchase_price,
            'selling_price' => $selling_price,
            'stock_quantity' => $stock_quantity,
            'min_stock' => $min_stock,
            'is_active' => $is_active,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$id]);
        
        $message = 'Produk berhasil diperbarui';
        
    } else {
        // Create new product
        
        // Check if code is unique
        $existing = $db->fetchOne("SELECT id FROM products WHERE code = ?", [$code]);
        if ($existing) {
            throw new Exception('Kode produk sudah digunakan');
        }
        
        $id = $db->insert('products', [
            'code' => $code,
            'name' => $name,
            'description' => $description,
            'category_id' => $category_id,
            'unit' => $unit,
            'purchase_price' => $purchase_price,
            'selling_price' => $selling_price,
            'stock_quantity' => $stock_quantity,
            'min_stock' => $min_stock,
            'is_active' => $is_active
        ]);
        
        $message = 'Produk berhasil ditambahkan';
    }
    
    $db->commit();
    
    jsonResponse(true, $message, ['id' => $id]);
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    jsonResponse(false, $e->getMessage());
}
?>
