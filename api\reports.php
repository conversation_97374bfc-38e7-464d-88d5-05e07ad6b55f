<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }

    $dateFrom = $_POST['date_from'] ?? '';
    $dateTo = $_POST['date_to'] ?? '';
    $type = $_POST['type'] ?? 'summary';

    if (empty($dateFrom) || empty($dateTo)) {
        throw new Exception('Rentang tanggal diperlukan');
    }

    // Get summary data
    $summary = $db->fetchOne("
        SELECT
            COUNT(*) as total_transactions,
            COALESCE(SUM(total_amount), 0) as total_sales,
            COALESCE(SUM(total_cost), 0) as total_cost,
            COALESCE(SUM(profit), 0) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
    ", [$dateFrom, $dateTo]);

    $profitMargin = $summary['total_cost'] > 0
        ? round(($summary['total_profit'] / $summary['total_cost']) * 100, 1)
        : 0;

    $summary['profit_margin'] = $profitMargin;

    $response = [
        'summary' => $summary,
        'title' => '',
        'html' => '',
        'chart_data' => null
    ];

    switch ($type) {
        case 'summary':
            $response = array_merge($response, generateSummaryReport($dateFrom, $dateTo));
            break;

        case 'daily':
            $response = array_merge($response, generateDailyReport($dateFrom, $dateTo));
            break;

        case 'product':
            $response = array_merge($response, generateProductReport($dateFrom, $dateTo));
            break;

        case 'customer':
            $response = array_merge($response, generateCustomerReport($dateFrom, $dateTo));
            break;

        default:
            throw new Exception('Jenis laporan tidak valid');
    }

    $response['summary'] = $summary;

    jsonResponse(true, 'Laporan berhasil di-generate', $response);

} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}

function generateSummaryReport($dateFrom, $dateTo) {
    global $db;

    $title = 'Laporan Ringkasan Penjualan';

    // Get top products
    $topProducts = $db->fetchAll("
        SELECT
            si.product_name,
            SUM(si.quantity) as total_qty,
            SUM(si.total_price) as total_sales,
            SUM(si.profit) as total_profit
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE s.sale_date BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_name
        ORDER BY total_sales DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    // Get top customers
    $topCustomers = $db->fetchAll("
        SELECT
            customer_name,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_purchases
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY customer_id, customer_name
        ORDER BY total_purchases DESC
        LIMIT 10
    ", [$dateFrom, $dateTo]);

    $html = '<div class="row">';

    // Top Products
    $html .= '<div class="col-md-6">
        <h6>Produk Terlaris</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>Produk</th>
                        <th>Qty</th>
                        <th>Penjualan</th>
                        <th>Laba</th>
                    </tr>
                </thead>
                <tbody>';

    foreach ($topProducts as $product) {
        $html .= '<tr>
            <td>' . htmlspecialchars($product['product_name']) . '</td>
            <td>' . $product['total_qty'] . '</td>
            <td>' . formatRupiah($product['total_sales']) . '</td>
            <td>' . formatRupiah($product['total_profit']) . '</td>
        </tr>';
    }

    $html .= '</tbody></table></div></div>';

    // Top Customers
    $html .= '<div class="col-md-6">
        <h6>Pelanggan Terbaik</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>Pelanggan</th>
                        <th>Transaksi</th>
                        <th>Total Beli</th>
                    </tr>
                </thead>
                <tbody>';

    foreach ($topCustomers as $customer) {
        $html .= '<tr>
            <td>' . htmlspecialchars($customer['customer_name']) . '</td>
            <td>' . $customer['total_transactions'] . '</td>
            <td>' . formatRupiah($customer['total_purchases']) . '</td>
        </tr>';
    }

    $html .= '</tbody></table></div></div></div>';

    return [
        'title' => $title,
        'html' => $html
    ];
}

function generateDailyReport($dateFrom, $dateTo) {
    global $db;

    $title = 'Laporan Penjualan Harian';

    $dailyData = $db->fetchAll("
        SELECT
            sale_date,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            SUM(profit) as total_profit
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY sale_date
        ORDER BY sale_date
    ", [$dateFrom, $dateTo]);

    $html = '<div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Tanggal</th>
                    <th>Transaksi</th>
                    <th>Penjualan</th>
                    <th>Laba</th>
                    <th>Margin</th>
                </tr>
            </thead>
            <tbody>';

    $chartLabels = [];
    $chartSales = [];
    $chartProfit = [];

    foreach ($dailyData as $day) {
        $margin = $day['total_sales'] > 0
            ? round(($day['total_profit'] / $day['total_sales']) * 100, 1)
            : 0;

        $html .= '<tr>
            <td>' . formatDate($day['sale_date']) . '</td>
            <td>' . $day['total_transactions'] . '</td>
            <td>' . formatRupiah($day['total_sales']) . '</td>
            <td>' . formatRupiah($day['total_profit']) . '</td>
            <td>' . $margin . '%</td>
        </tr>';

        $chartLabels[] = formatDate($day['sale_date']);
        $chartSales[] = $day['total_sales'];
        $chartProfit[] = $day['total_profit'];
    }

    $html .= '</tbody></table></div>';

    return [
        'title' => $title,
        'html' => $html,
        'chart_data' => [
            'labels' => $chartLabels,
            'sales' => $chartSales,
            'profit' => $chartProfit
        ]
    ];
}

function generateProductReport($dateFrom, $dateTo) {
    global $db;

    $title = 'Laporan Penjualan Per Produk';

    $productData = $db->fetchAll("
        SELECT
            si.product_code,
            si.product_name,
            SUM(si.quantity) as total_qty,
            SUM(si.total_price) as total_sales,
            SUM(si.total_cost) as total_cost,
            SUM(si.profit) as total_profit,
            AVG(si.unit_price) as avg_price
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE s.sale_date BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_code, si.product_name
        ORDER BY total_sales DESC
    ", [$dateFrom, $dateTo]);

    $html = '<div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Kode</th>
                    <th>Produk</th>
                    <th>Qty</th>
                    <th>Rata-rata Harga</th>
                    <th>Total Penjualan</th>
                    <th>Total HPP</th>
                    <th>Laba</th>
                    <th>Margin</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($productData as $product) {
        $margin = $product['total_cost'] > 0
            ? round(($product['total_profit'] / $product['total_cost']) * 100, 1)
            : 0;

        $html .= '<tr>
            <td>' . htmlspecialchars($product['product_code']) . '</td>
            <td>' . htmlspecialchars($product['product_name']) . '</td>
            <td>' . $product['total_qty'] . '</td>
            <td>' . formatRupiah($product['avg_price']) . '</td>
            <td>' . formatRupiah($product['total_sales']) . '</td>
            <td>' . formatRupiah($product['total_cost']) . '</td>
            <td>' . formatRupiah($product['total_profit']) . '</td>
            <td>' . $margin . '%</td>
        </tr>';
    }

    $html .= '</tbody></table></div>';

    return [
        'title' => $title,
        'html' => $html
    ];
}

function generateCustomerReport($dateFrom, $dateTo) {
    global $db;

    $title = 'Laporan Penjualan Per Pelanggan';

    $customerData = $db->fetchAll("
        SELECT
            customer_name,
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_purchases,
            SUM(profit) as total_profit,
            AVG(total_amount) as avg_transaction
        FROM sales
        WHERE sale_date BETWEEN ? AND ?
        GROUP BY customer_id, customer_name
        ORDER BY total_purchases DESC
    ", [$dateFrom, $dateTo]);

    $html = '<div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Pelanggan</th>
                    <th>Total Transaksi</th>
                    <th>Total Pembelian</th>
                    <th>Rata-rata Transaksi</th>
                    <th>Total Laba</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($customerData as $customer) {
        $html .= '<tr>
            <td>' . htmlspecialchars($customer['customer_name']) . '</td>
            <td>' . $customer['total_transactions'] . '</td>
            <td>' . formatRupiah($customer['total_purchases']) . '</td>
            <td>' . formatRupiah($customer['avg_transaction']) . '</td>
            <td>' . formatRupiah($customer['total_profit']) . '</td>
        </tr>';
    }

    $html .= '</tbody></table></div>';

    return [
        'title' => $title,
        'html' => $html
    ];
}
?>
