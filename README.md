# Aplikasi POS (Point of Sale) Sederhana

Aplikasi Point of Sale untuk penjualan kusen pintu, kusen jendela, daun pintu, daun jendela, ventilasi dan produk terkait.

## Fitur Utama

- ✅ **Dashboard** - Ringkasan penjualan dan statistik
- ✅ **Master Data Produk** - CRUD produk dengan kategori
- ✅ **Data Pelanggan** - Manajemen data customer
- ✅ **Point of Sale** - Transaksi penjualan
- ✅ **Riwayat Penjualan** - History transaksi
- ✅ **Laporan Laba** - Laporan profit dan analisis

## Teknologi yang Digunakan

- **Backend**: PHP 7.4+ dengan MySQLi
- **Database**: MariaDB/MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework CSS**: Bootstrap 5
- **Table Plugin**: DataTables dengan server-side processing
- **Icons**: Font Awesome 6
- **Alerts**: SweetAlert2

## Persyaratan Sistem

- AppServ, XAMPP, atau WAMP
- PHP 7.4 atau lebih tinggi
- MariaDB/MySQL 5.7 atau lebih tinggi
- Web browser modern (Chrome, Firefox, Safari, Edge)

## Instalasi

### 1. Setup Database

1. Pastikan AppServ/MySQL sudah berjalan
2. Buka phpMyAdmin atau MySQL client
3. Import file `database_schema.sql` untuk membuat database dan tabel
4. Atau jalankan SQL berikut untuk membuat database:

```sql
CREATE DATABASE adi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. Konfigurasi Database

Database sudah dikonfigurasi dengan kredensial:
- **Host**: localhost
- **Username**: root
- **Password**: 8@n90N3!
- **Database**: adi

Jika perlu mengubah, edit file `config/database.php`

### 3. Test Koneksi

Akses file test berikut di browser:

1. **Test MySQLi Extension**: `test_mysqli.php`
   - Cek apakah MySQLi extension aktif
   - Test koneksi database dasar

2. **Test Application Database**: `test_database.php`
   - Verifikasi koneksi database berhasil
   - Cek tabel sudah dibuat
   - Verifikasi data sample tersedia

### 4. Akses Aplikasi

Buka browser dan akses:
```
http://localhost/adi/
```

## Struktur Project

```
/adi/
├── api/                    # PHP API endpoints
│   ├── products.php        # DataTables API untuk produk
│   ├── product_save.php    # Save/update produk
│   ├── product_get.php     # Get detail produk
│   └── product_delete.php  # Delete produk
├── assets/                 # Static files
│   ├── css/
│   │   └── style.css       # Custom CSS
│   └── js/
│       └── app.js          # Custom JavaScript
├── config/                 # Konfigurasi
│   └── database.php        # Database connection
├── includes/               # PHP includes
│   ├── header.php          # Template header
│   ├── footer.php          # Template footer
│   └── functions.php       # Helper functions
├── pages/                  # Halaman aplikasi
│   ├── products.php        # Halaman produk
│   ├── categories.php      # Halaman kategori
│   ├── customers.php       # Halaman pelanggan
│   ├── pos.php            # Point of sale
│   ├── sales.php          # Riwayat penjualan
│   └── reports.php        # Laporan
├── database_schema.sql     # Database schema
├── test_database.php       # Test koneksi database
├── index.php              # Dashboard utama
└── README.md              # Dokumentasi
```

## Penggunaan

### 1. Dashboard
- Lihat ringkasan penjualan hari ini dan bulan ini
- Monitor produk dengan stok rendah
- Akses cepat ke fitur utama

### 2. Data Produk
- Tambah, edit, hapus produk
- Kategorisasi produk
- Set harga beli dan jual
- Monitor stok dan stok minimum
- Search dan filter dengan DataTables

### 3. Point of Sale
- Pilih produk untuk transaksi
- Input quantity
- Hitung total otomatis
- Cetak struk penjualan

### 4. Laporan
- Laporan penjualan per periode
- Analisis laba rugi
- Export data ke CSV/Excel

## Fitur DataTables

- **Server-side processing** untuk performa optimal
- **Responsive design** untuk mobile
- **Search dan filter** real-time
- **Sorting** multi-kolom
- **Pagination** otomatis
- **Export** data ke berbagai format

## Keamanan

- Input sanitization
- SQL injection protection dengan PDO
- XSS protection
- CSRF protection (akan ditambahkan)

## Pengembangan Selanjutnya

- [ ] Sistem login dan user management
- [ ] Backup dan restore database
- [ ] Integrasi printer thermal
- [ ] Mobile app (PWA)
- [ ] Multi-cabang/toko
- [ ] Inventory management yang lebih advanced
- [ ] Integrasi payment gateway

## Troubleshooting

### MySQLi Extension Not Found
1. Edit file php.ini
2. Uncomment: `extension=mysqli`
3. Restart AppServ

### Database Connection Error
1. Pastikan AppServ/MySQL berjalan
2. Cek kredensial database di `config/database.php`
3. Pastikan database 'adi' sudah dibuat
4. Test dengan `test_mysqli.php` terlebih dahulu

### DataTables Not Loading
1. Cek koneksi internet untuk CDN
2. Pastikan jQuery loaded sebelum DataTables
3. Cek console browser untuk error JavaScript

### Permission Denied
1. Pastikan folder memiliki permission yang tepat
2. Cek ownership file dan folder

## Support

Jika mengalami masalah atau butuh bantuan pengembangan lebih lanjut, silakan hubungi developer.

## License

Aplikasi ini dibuat untuk keperluan internal dan pembelajaran.
