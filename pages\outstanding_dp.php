<?php
// Set timezone to Makassar, Indonesia (WITA - UTC+8)
date_default_timezone_set('Asia/Makassar');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

$pageTitle = 'Outstanding DP Management';
$currentPage = 'outstanding_dp';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Outstanding DP Management
                    </h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success btn-sm" id="refreshData">
                            <i class="fas fa-sync-alt me-1"></i>
                            Refresh
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="exportOutstanding">
                            <i class="fas fa-file-excel me-1"></i>
                            Export Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Outstanding</h6>
                                            <h4 id="totalOutstanding">Rp 0</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Jumlah Customer</h6>
                                            <h4 id="totalCustomers">0</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Rata-rata Sisa</h6>
                                            <h4 id="averageRemaining">Rp 0</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-chart-line fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Terbayar</h6>
                                            <h4 id="totalPaid">Rp 0</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput"
                                       placeholder="Cari customer atau invoice...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">Semua Status</option>
                                <option value="partial">Belum Lunas</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="sortBy">
                                <option value="sale_date DESC">Terbaru</option>
                                <option value="sale_date ASC">Terlama</option>
                                <option value="remaining_amount DESC">Sisa Terbesar</option>
                                <option value="remaining_amount ASC">Sisa Terkecil</option>
                                <option value="customer_name ASC">Nama A-Z</option>
                            </select>
                        </div>
                    </div>

                    <!-- Outstanding DP Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="outstandingTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Customer</th>
                                    <th>Invoice</th>
                                    <th>Tanggal</th>
                                    <th>Total Transaksi</th>
                                    <th>Sudah Dibayar</th>
                                    <th>Sisa Bayar</th>
                                    <th>Progress</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="outstandingTableBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                                        <p>Memuat data...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    Pembayaran Cicilan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="paymentDetails"></div>

                <div class="mb-3">
                    <label for="installmentAmount" class="form-label">Jumlah Pembayaran</label>
                    <input type="text" class="form-control currency-input" id="installmentAmount" required>
                    <div class="form-text">Masukkan jumlah pembayaran cicilan</div>
                </div>

                <div class="mb-3">
                    <label for="installmentNotes" class="form-label">Catatan</label>
                    <textarea class="form-control" id="installmentNotes" rows="3"
                              placeholder="Catatan pembayaran (opsional)"></textarea>
                </div>

                <div id="paymentSummary" class="alert alert-info"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="processPayment">
                    <i class="fas fa-save me-1"></i>
                    Proses Pembayaran
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Detail Transaksi & History Pembayaran
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailModalBody">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
// Set additional JS to be loaded after jQuery
$additionalJS = '
<!-- SheetJS for Excel Export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<!-- Outstanding DP Script with jQuery check -->
<script>
// Wait for jQuery to be available
function waitForJQuery() {
    if (typeof jQuery !== "undefined") {
        console.log("jQuery is ready, loading outstanding_dp.js");

        // Load the outstanding DP script
        var script = document.createElement("script");
        script.src = "../assets/js/outstanding_dp.js";
        script.onload = function() {
            console.log("outstanding_dp.js loaded successfully");
        };
        script.onerror = function() {
            console.error("Failed to load outstanding_dp.js");
        };
        document.head.appendChild(script);
    } else {
        console.log("Waiting for jQuery...");
        setTimeout(waitForJQuery, 100);
    }
}

// Start waiting for jQuery
waitForJQuery();
</script>
';

require_once '../includes/footer.php';
?>
