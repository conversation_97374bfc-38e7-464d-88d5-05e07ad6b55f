<?php
/**
 * Simple XLSX Generator
 * A lightweight class to generate basic XLSX files without external dependencies
 */
class SimpleXLSXGen {
    private $data = [];
    private $styles = [];
    
    public function __construct() {
        // Initialize basic styles
        $this->styles = [
            'header' => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => ['color' => 'FF4472C4'],
                'font_color' => 'FFFFFFFF'
            ],
            'title' => [
                'font' => ['bold' => true, 'size' => 14]
            ],
            'currency' => [
                'number_format' => '#,##0'
            ]
        ];
    }
    
    public function addData($data) {
        $this->data = $data;
    }
    
    public function output() {
        // Create temporary directory for XLSX files
        $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
        mkdir($tempDir);
        
        // Create XLSX structure
        $this->createXLSXStructure($tempDir);
        
        // Create ZIP file
        $zipFile = $tempDir . '.xlsx';
        $this->createZip($tempDir, $zipFile);
        
        // Output file
        readfile($zipFile);
        
        // Cleanup
        $this->cleanup($tempDir);
        unlink($zipFile);
    }
    
    private function createXLSXStructure($tempDir) {
        // Create directories
        mkdir($tempDir . '/_rels');
        mkdir($tempDir . '/docProps');
        mkdir($tempDir . '/xl');
        mkdir($tempDir . '/xl/_rels');
        mkdir($tempDir . '/xl/worksheets');
        
        // Create [Content_Types].xml
        file_put_contents($tempDir . '/[Content_Types].xml', $this->getContentTypes());
        
        // Create _rels/.rels
        file_put_contents($tempDir . '/_rels/.rels', $this->getRels());
        
        // Create docProps/app.xml
        file_put_contents($tempDir . '/docProps/app.xml', $this->getApp());
        
        // Create docProps/core.xml
        file_put_contents($tempDir . '/docProps/core.xml', $this->getCore());
        
        // Create xl/workbook.xml
        file_put_contents($tempDir . '/xl/workbook.xml', $this->getWorkbook());
        
        // Create xl/_rels/workbook.xml.rels
        file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $this->getWorkbookRels());
        
        // Create xl/worksheets/sheet1.xml
        file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $this->getWorksheet());
        
        // Create xl/sharedStrings.xml
        file_put_contents($tempDir . '/xl/sharedStrings.xml', $this->getSharedStrings());
        
        // Create xl/styles.xml
        file_put_contents($tempDir . '/xl/styles.xml', $this->getStyles());
    }
    
    private function getContentTypes() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
    <Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml"/>
    <Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml"/>
    <Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"/>
</Types>';
    }
    
    private function getRels() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/>
    <Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/>
</Relationships>';
    }
    
    private function getApp() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
    <Application>POS System</Application>
    <DocSecurity>0</DocSecurity>
    <ScaleCrop>false</ScaleCrop>
    <Company>Toko Kusen Jaya</Company>
    <LinksUpToDate>false</LinksUpToDate>
    <SharedDoc>false</SharedDoc>
    <HyperlinksChanged>false</HyperlinksChanged>
    <AppVersion>1.0</AppVersion>
</Properties>';
    }
    
    private function getCore() {
        $now = date('c');
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <dc:creator>POS System</dc:creator>
    <dcterms:created xsi:type="dcterms:W3CDTF">' . $now . '</dcterms:created>
    <dcterms:modified xsi:type="dcterms:W3CDTF">' . $now . '</dcterms:modified>
    <dc:title>Laporan Penjualan</dc:title>
    <dc:subject>Sales Report</dc:subject>
</cp:coreProperties>';
    }
    
    private function getWorkbook() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="Laporan" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    }
    
    private function getWorkbookRels() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
    <Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/>
</Relationships>';
    }
    
    private function getSharedStrings() {
        $strings = [];
        $stringMap = [];
        $count = 0;
        
        // Collect all strings
        foreach ($this->data as $row) {
            foreach ($row as $cell) {
                if (is_string($cell) && !is_numeric($cell)) {
                    if (!isset($stringMap[$cell])) {
                        $stringMap[$cell] = $count++;
                        $strings[] = $cell;
                    }
                }
            }
        }
        
        $xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
        
        foreach ($strings as $string) {
            $xml .= '<si><t>' . htmlspecialchars($string, ENT_XML1) . '</t></si>';
        }
        
        $xml .= '</sst>';
        return $xml;
    }
    
    private function getStyles() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <fonts count="2">
        <font><sz val="11"/><name val="Calibri"/></font>
        <font><b/><sz val="12"/><name val="Calibri"/></font>
    </fonts>
    <fills count="2">
        <fill><patternFill patternType="none"/></fill>
        <fill><patternFill patternType="gray125"/></fill>
    </fills>
    <borders count="1">
        <border><left/><right/><top/><bottom/><diagonal/></border>
    </borders>
    <cellStyleXfs count="1">
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0"/>
    </cellStyleXfs>
    <cellXfs count="2">
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" xfId="0"/>
        <xf numFmtId="0" fontId="1" fillId="0" borderId="0" xfId="0"/>
    </cellXfs>
</styleSheet>';
    }
    
    private function getWorksheet() {
        $xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheetData>';
        
        $rowNum = 1;
        foreach ($this->data as $row) {
            $xml .= '<row r="' . $rowNum . '">';
            $colNum = 1;
            foreach ($row as $cell) {
                $cellRef = $this->getCellReference($colNum, $rowNum);
                if (is_numeric($cell)) {
                    $xml .= '<c r="' . $cellRef . '"><v>' . $cell . '</v></c>';
                } else {
                    $stringIndex = $this->getStringIndex($cell);
                    $xml .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
                }
                $colNum++;
            }
            $xml .= '</row>';
            $rowNum++;
        }
        
        $xml .= '</sheetData></worksheet>';
        return $xml;
    }
    
    private function getCellReference($col, $row) {
        $colLetter = '';
        while ($col > 0) {
            $col--;
            $colLetter = chr(65 + ($col % 26)) . $colLetter;
            $col = intval($col / 26);
        }
        return $colLetter . $row;
    }
    
    private function getStringIndex($string) {
        static $stringMap = null;
        if ($stringMap === null) {
            $stringMap = [];
            $index = 0;
            foreach ($this->data as $row) {
                foreach ($row as $cell) {
                    if (is_string($cell) && !is_numeric($cell)) {
                        if (!isset($stringMap[$cell])) {
                            $stringMap[$cell] = $index++;
                        }
                    }
                }
            }
        }
        return isset($stringMap[$string]) ? $stringMap[$string] : 0;
    }
    
    private function createZip($source, $destination) {
        $zip = new ZipArchive();
        if ($zip->open($destination, ZipArchive::CREATE) !== TRUE) {
            throw new Exception('Cannot create ZIP file');
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                $zip->addEmptyDir(str_replace($source . '/', '', $file . '/'));
            } else if ($file->isFile()) {
                $zip->addFile($file, str_replace($source . '/', '', $file));
            }
        }
        
        $zip->close();
    }
    
    private function cleanup($dir) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file);
            } else {
                unlink($file);
            }
        }
        rmdir($dir);
    }
}
?>
